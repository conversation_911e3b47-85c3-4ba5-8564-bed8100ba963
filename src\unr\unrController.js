/**
 * UNR集成控制器
 * 
 * 功能：
 * 1. 统一管理UNR相关的所有组件
 * 2. 提供高级API接口
 * 3. 处理UNR特有的业务逻辑
 * 4. 集成页面分析、表单填写和步骤处理
 * 
 * <AUTHOR> Registration Bot
 */

import { UNRPageAnalyzer } from './pageAnalyzer.js';
import { UNRFormFiller } from './formFiller.js';
import { UNRStepProcessor } from './stepProcessor.js';

/**
 * UNR集成控制器类
 */
export class UNRController {
    constructor(browserController, config = {}) {
        this.browser = browserController;
        this.logger = config.logger || null;
        
        this.config = {
            baseUrl: config.baseUrl || 'https://admissions.unr.edu',
            timeout: config.timeout || 30000,
            retryCount: config.retryCount || 3,
            enablePageAnalysis: config.enablePageAnalysis !== false,
            enableAdaptiveStrategy: config.enableAdaptiveStrategy !== false,
            ...config
        };
        
        // 初始化子组件
        this.pageAnalyzer = new UNRPageAnalyzer(browserController, {
            ...config,
            logger: this.logger
        });
        
        this.formFiller = new UNRFormFiller(browserController, {
            ...config,
            logger: this.logger
        });
        
        this.stepProcessor = new UNRStepProcessor(browserController, {
            ...config,
            logger: this.logger
        });
        
        // UNR特定配置
        this.unrConfig = {
            urls: {
                home: `${this.config.baseUrl}`,
                register: `${this.config.baseUrl}/account/register`,
                login: `${this.config.baseUrl}/account/login`,
                apply: `${this.config.baseUrl}/apply/`,
                dashboard: `${this.config.baseUrl}/dashboard`
            },
            
            // 页面识别标识
            pageIdentifiers: {
                registration: {
                    title: ['Create Account', 'Register', 'Sign Up'],
                    elements: ['#email', '#password', '#confirmPassword']
                },
                login: {
                    title: ['Sign In', 'Login', 'Log In'],
                    elements: ['input[name="email"]', 'input[name="password"]']
                },
                application: {
                    title: ['Application', 'Apply', 'Admission Application'],
                    elements: ['.application-form', '.step-indicator', '.progress-bar']
                },
                dashboard: {
                    title: ['Dashboard', 'My Account', 'Student Portal'],
                    elements: ['.dashboard', '.account-info', '.user-profile']
                }
            }
        };
        
        this.currentPage = null;
        this.pageStructures = new Map();
        this.sessionData = {};
    }

    /**
     * 执行完整的UNR注册流程
     */
    async executeRegistrationFlow(studentData) {
        try {
            this.log('info', 'Starting UNR registration flow', {
                studentEmail: studentData.email
            });
            
            // 1. 导航到注册页面
            await this.navigateToRegistrationPage();
            
            // 2. 分析页面结构（如果启用）
            if (this.config.enablePageAnalysis) {
                await this.analyzeCurrentPage('registration');
            }
            
            // 3. 填写注册表单
            await this.fillRegistrationForm(studentData);
            
            // 4. 提交注册
            await this.submitRegistrationForm();
            
            // 5. 处理注册后的流程（邮件验证等）
            const result = await this.handlePostRegistration(studentData);
            
            this.log('info', 'UNR registration flow completed', result);
            
            return {
                success: true,
                ...result
            };
            
        } catch (error) {
            this.log('error', 'UNR registration flow failed', {
                error: error.message,
                currentPage: this.currentPage
            });
            throw error;
        }
    }

    /**
     * 执行完整的UNR申请流程
     */
    async executeApplicationFlow(studentData) {
        try {
            this.log('info', 'Starting UNR application flow', {
                studentEmail: studentData.email
            });
            
            // 1. 确保已登录
            await this.ensureLoggedIn(studentData);
            
            // 2. 导航到申请页面
            await this.navigateToApplicationPage();
            
            // 3. 分析申请页面结构
            if (this.config.enablePageAnalysis) {
                await this.analyzeCurrentPage('application');
            }
            
            // 4. 执行申请步骤
            const result = await this.stepProcessor.processAllSteps(studentData);
            
            this.log('info', 'UNR application flow completed', result);
            
            return result;
            
        } catch (error) {
            this.log('error', 'UNR application flow failed', {
                error: error.message,
                currentPage: this.currentPage
            });
            throw error;
        }
    }

    /**
     * 导航到注册页面
     */
    async navigateToRegistrationPage() {
        this.log('info', 'Navigating to registration page');
        
        await this.browser.navigateTo(this.unrConfig.urls.register);
        await this.browser.waitForLoadState('domcontentloaded');
        
        // 验证页面是否正确加载
        const isCorrectPage = await this.verifyPage('registration');
        if (!isCorrectPage) {
            throw new Error('Failed to navigate to registration page');
        }
        
        this.currentPage = 'registration';
        this.log('info', 'Successfully navigated to registration page');
    }

    /**
     * 导航到申请页面
     */
    async navigateToApplicationPage() {
        this.log('info', 'Navigating to application page');
        
        await this.browser.navigateTo(this.unrConfig.urls.apply);
        await this.browser.waitForLoadState('domcontentloaded');
        
        // 验证页面是否正确加载
        const isCorrectPage = await this.verifyPage('application');
        if (!isCorrectPage) {
            throw new Error('Failed to navigate to application page');
        }
        
        this.currentPage = 'application';
        this.log('info', 'Successfully navigated to application page');
    }

    /**
     * 确保用户已登录
     */
    async ensureLoggedIn(studentData) {
        this.log('info', 'Checking login status');
        
        // 尝试访问需要登录的页面
        await this.browser.navigateTo(this.unrConfig.urls.dashboard);
        await this.browser.sleep(2000);
        
        const currentUrl = await this.browser.getCurrentUrl();
        
        // 如果被重定向到登录页面，执行登录
        if (currentUrl.includes('login') || currentUrl.includes('signin')) {
            this.log('info', 'User not logged in, performing login');
            await this.performLogin(studentData);
        } else {
            this.log('info', 'User already logged in');
        }
    }

    /**
     * 执行登录操作
     */
    async performLogin(studentData) {
        try {
            this.log('info', 'Performing login operation');
            
            // 填写登录表单
            await this.formFiller.fillField(
                ['#email', 'input[name="email"]', 'input[type="email"]'],
                studentData.email,
                'Login Email'
            );
            
            await this.formFiller.fillField(
                ['#password', 'input[name="password"]', 'input[type="password"]'],
                studentData.password,
                'Login Password'
            );
            
            // 点击登录按钮
            const loginSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("Sign In")',
                'button:contains("Login")',
                '#login-btn'
            ];
            
            for (const selector of loginSelectors) {
                try {
                    await this.browser.waitForElement(selector, { timeout: 3000 });
                    await this.browser.clickElement(selector);
                    this.log('info', 'Login button clicked');
                    break;
                } catch (error) {
                    continue;
                }
            }
            
            // 等待登录完成
            await this.browser.sleep(3000);
            
            // 验证登录是否成功
            const currentUrl = await this.browser.getCurrentUrl();
            if (!currentUrl.includes('login')) {
                this.log('info', 'Login successful');
            } else {
                throw new Error('Login failed - still on login page');
            }
            
        } catch (error) {
            this.log('error', 'Login operation failed', { error: error.message });
            throw error;
        }
    }

    /**
     * 填写注册表单
     */
    async fillRegistrationForm(studentData) {
        this.log('info', 'Filling registration form');
        
        await this.formFiller.fillRegistrationForm(studentData);
        
        this.log('info', 'Registration form filled successfully');
    }

    /**
     * 提交注册表单
     */
    async submitRegistrationForm() {
        this.log('info', 'Submitting registration form');
        
        const submitSelectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            'button:contains("Register")',
            'button:contains("Create Account")',
            '#register-btn',
            '#submit-btn'
        ];
        
        for (const selector of submitSelectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.clickElement(selector);
                this.log('info', 'Registration form submitted');
                
                // 等待提交处理
                await this.browser.sleep(3000);
                return;
                
            } catch (error) {
                continue;
            }
        }
        
        throw new Error('Failed to submit registration form - no submit button found');
    }

    /**
     * 处理注册后的流程
     */
    async handlePostRegistration(studentData) {
        this.log('info', 'Handling post-registration process');
        
        // 检查当前页面状态
        const currentUrl = await this.browser.getCurrentUrl();
        const pageTitle = await this.browser.getPageTitle();
        
        this.log('info', 'Post-registration page info', {
            url: currentUrl,
            title: pageTitle
        });
        
        // 根据页面状态决定下一步操作
        if (currentUrl.includes('verify') || pageTitle.toLowerCase().includes('verify')) {
            this.log('info', 'Email verification required');
            return {
                status: 'email_verification_required',
                nextStep: 'email_verification'
            };
        } else if (currentUrl.includes('dashboard') || currentUrl.includes('apply')) {
            this.log('info', 'Registration completed, redirected to dashboard/application');
            return {
                status: 'registration_complete',
                nextStep: 'application'
            };
        } else {
            this.log('info', 'Registration status unclear, manual verification needed');
            return {
                status: 'unknown',
                nextStep: 'manual_verification'
            };
        }
    }

    /**
     * 分析当前页面
     */
    async analyzeCurrentPage(pageType) {
        try {
            this.log('info', `Analyzing ${pageType} page structure`);
            
            let structure;
            switch (pageType) {
                case 'registration':
                    structure = await this.pageAnalyzer.analyzeRegistrationPage();
                    break;
                case 'application':
                    structure = await this.pageAnalyzer.analyzeApplicationPage();
                    break;
                default:
                    throw new Error(`Unknown page type: ${pageType}`);
            }
            
            this.pageStructures.set(pageType, structure);
            
            // 如果启用自适应策略，更新表单填写器配置
            if (this.config.enableAdaptiveStrategy) {
                await this.updateAdaptiveStrategy(pageType, structure);
            }
            
            this.log('info', `${pageType} page analysis completed`);
            
        } catch (error) {
            this.log('warn', `Failed to analyze ${pageType} page`, {
                error: error.message
            });
        }
    }

    /**
     * 更新自适应策略
     */
    async updateAdaptiveStrategy(pageType, structure) {
        this.log('info', `Updating adaptive strategy for ${pageType} page`);
        
        // 根据页面结构分析结果更新表单填写策略
        const fieldMapping = this.pageAnalyzer.generateFieldMapping(pageType);
        
        // 更新表单填写器的字段映射
        if (pageType === 'registration') {
            Object.assign(this.formFiller.unrFieldMappings.registration, fieldMapping);
        } else if (pageType === 'application') {
            Object.assign(this.formFiller.unrFieldMappings.application, fieldMapping);
        }
        
        this.log('info', 'Adaptive strategy updated', {
            pageType,
            fieldsUpdated: Object.keys(fieldMapping).length
        });
    }

    /**
     * 验证页面类型
     */
    async verifyPage(expectedPageType) {
        try {
            const pageTitle = await this.browser.getPageTitle();
            const currentUrl = await this.browser.getCurrentUrl();
            
            const identifier = this.unrConfig.pageIdentifiers[expectedPageType];
            if (!identifier) {
                return false;
            }
            
            // 检查标题
            const titleMatch = identifier.title.some(title => 
                pageTitle.toLowerCase().includes(title.toLowerCase())
            );
            
            // 检查关键元素
            let elementMatch = false;
            for (const selector of identifier.elements) {
                try {
                    await this.browser.waitForElement(selector, { timeout: 2000 });
                    elementMatch = true;
                    break;
                } catch (error) {
                    continue;
                }
            }
            
            const isCorrectPage = titleMatch || elementMatch;
            
            this.log('debug', 'Page verification result', {
                expectedPageType,
                currentUrl,
                pageTitle,
                titleMatch,
                elementMatch,
                isCorrectPage
            });
            
            return isCorrectPage;
            
        } catch (error) {
            this.log('warn', 'Page verification failed', { error: error.message });
            return false;
        }
    }

    /**
     * 获取当前会话状态
     */
    getSessionStatus() {
        return {
            currentPage: this.currentPage,
            completedSteps: this.stepProcessor.completedSteps,
            sessionData: this.sessionData,
            pageStructures: Object.fromEntries(this.pageStructures)
        };
    }

    /**
     * 保存会话数据
     */
    async saveSessionData(outputPath) {
        const sessionData = {
            timestamp: new Date().toISOString(),
            currentPage: this.currentPage,
            completedSteps: this.stepProcessor.completedSteps,
            pageStructures: Object.fromEntries(this.pageStructures),
            sessionData: this.sessionData
        };
        
        const fs = await import('fs-extra');
        await fs.writeFile(outputPath, JSON.stringify(sessionData, null, 2));
        
        this.log('info', 'Session data saved', { outputPath });
        
        return sessionData;
    }

    /**
     * 日志记录辅助方法
     */
    log(level, message, meta = {}) {
        if (this.logger) {
            this.logger.log(level, message, { ...meta, component: 'UNRController' });
        } else {
            console.log(`[${level.toUpperCase()}] ${message}`, meta);
        }
    }
}
