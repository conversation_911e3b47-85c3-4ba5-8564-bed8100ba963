# UNR自动注册和申请系统

基于Puppeteer的内华达大学里诺分校(University of Nevada, Reno)自动注册和申请系统。

## 功能特性

- 🤖 **自动化注册**: 自动填写注册表单并完成邮件验证
- 📋 **智能申请**: 自动完成10个步骤的申请表单填写
- 📊 **Excel集成**: 从Excel文件读取学生信息数据
- 📧 **邮件验证**: 自动获取QQ邮箱验证PIN码
- 🔄 **错误恢复**: 智能重试和错误处理机制
- 📸 **调试支持**: 自动截图和详细日志记录

## 系统要求

- Node.js >= 18.0.0
- Windows 10/11 或 macOS 或 Linux
- Chrome/Chromium 浏览器
- 稳定的网络连接

## 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd unr-auto-registration
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置环境**
   ```bash
   cp .env.example .env
   # 编辑.env文件，配置邮箱等信息
   ```

4. **准备Excel数据文件**
   - 将包含学生信息的Excel文件命名为`test.xlsx`
   - 放置在项目根目录

## 使用方法

### 基本运行
```bash
npm start
```

### 开发模式（带调试）
```bash
npm run dev
```

### 测试模式
```bash
npm test
```

## Excel数据格式

Excel文件应包含以下工作表和字段：

### 主工作表（学生信息）
- 邮箱 (Email)
- 名 (First Name)  
- 姓 (Last Name)
- 生日 (Birth Date)
- 完整地址 (Full Address)
- 电话 (Phone)
- 性别 (Gender)
- 社会安全号 (SSN)

### 高中信息工作表
- 学校名称 (School Name)
- 其他相关信息

## 申请流程步骤

系统将自动完成以下10个申请步骤：

1. **Application Information** - 申请信息
2. **Nondegree Application Instructions** - 非学位申请说明
3. **Application Term** - 申请学期
4. **Personal Background** - 个人背景
5. **Background Continued** - 背景信息续
6. **Emergency Contact** - 紧急联系人
7. **Academic History** - 学术历史
8. **Program Selection** - 项目选择
9. **Signature** - 签名
10. **Review** - 审核提交

## 配置说明

### 邮件配置
```env
EMAIL_SERVER=imap.qq.com
EMAIL_PORT=993
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
```

### 浏览器配置
```env
BROWSER_HEADLESS=false  # true为无头模式
BROWSER_WIDTH=1280
BROWSER_HEIGHT=720
```

## 目录结构

```
unr-auto-registration/
├── src/
│   ├── main.js                 # 主程序入口
│   ├── utils/                  # 工具模块
│   │   ├── excelReader.js      # Excel读取
│   │   ├── emailHandler.js     # 邮件处理
│   │   └── logger.js           # 日志记录
│   └── automation/             # 自动化模块
│       ├── browserController.js # 浏览器控制
│       ├── registrationFlow.js  # 注册流程
│       └── applicationFlow.js   # 申请流程
├── logs/                       # 日志文件
├── screenshots/                # 截图文件
├── test.xlsx                   # 学生数据文件
├── package.json
├── .env                        # 环境配置
└── README.md
```

## 注意事项

⚠️ **重要提醒**：
- 请确保Excel文件中的数据准确无误
- 建议先在测试环境中运行
- 保护好邮箱密码等敏感信息
- 遵守网站的使用条款和政策

## 故障排除

### 常见问题

1. **浏览器启动失败**
   - 检查Chrome是否正确安装
   - 尝试设置`BROWSER_HEADLESS=true`

2. **邮件验证失败**
   - 检查QQ邮箱IMAP设置
   - 确认应用专用密码正确

3. **Excel读取错误**
   - 检查文件路径和格式
   - 确保工作表名称正确

## 技术支持

如遇问题，请检查：
1. 日志文件 (`logs/` 目录)
2. 错误截图 (`screenshots/` 目录)
3. 网络连接状态

## 许可证

MIT License
