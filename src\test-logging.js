/**
 * 日志系统测试脚本
 * 
 * 测试日志记录和分析功能
 * 
 * <AUTHOR> Registration Bot
 */

import { Logger } from './utils/logger.js';
import { LogAnalyzer } from './utils/logAnalyzer.js';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// 获取项目根目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

/**
 * 测试日志记录器初始化
 */
async function testLoggerInitialization() {
    console.log('🧪 测试1: 日志记录器初始化');
    console.log('================================');
    
    try {
        const logger = new Logger({
            level: 'debug',
            logDir: path.join(projectRoot, 'test-logs'),
            enableConsole: false,
            enableFile: true
        });
        
        // 等待初始化完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log('✅ 日志记录器初始化成功');
        console.log(`📁 日志目录: ${logger.config.logDir}`);
        console.log(`📊 会话ID: ${logger.sessionId}`);
        
        await logger.close();
        return true;
        
    } catch (error) {
        console.error('❌ 日志记录器初始化失败:', error.message);
        return false;
    }
}

/**
 * 测试基本日志记录功能
 */
async function testBasicLogging() {
    console.log('\n🧪 测试2: 基本日志记录功能');
    console.log('================================');
    
    try {
        const logger = new Logger({
            level: 'debug',
            logDir: path.join(projectRoot, 'test-logs'),
            enableConsole: false,
            enableFile: true
        });
        
        // 等待初始化完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 测试不同级别的日志
        logger.info('这是一条信息日志', { testData: 'info test' });
        logger.warn('这是一条警告日志', { testData: 'warn test' });
        logger.error('这是一条错误日志', { testData: 'error test' });
        logger.debug('这是一条调试日志', { testData: 'debug test' });
        
        // 测试不同分类的日志
        logger.logRegistration('info', '注册流程开始', { email: '<EMAIL>' });
        logger.logApplication('info', '申请流程开始', { step: 1 });
        logger.logEmail('info', '邮件发送成功', { recipient: '<EMAIL>' });
        logger.logBrowser('info', '浏览器启动', { headless: true });
        
        // 等待日志写入
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log('✅ 基本日志记录功能测试成功');
        
        await logger.close();
        return true;
        
    } catch (error) {
        console.error('❌ 基本日志记录功能测试失败:', error.message);
        return false;
    }
}

/**
 * 测试流程和步骤日志
 */
async function testFlowAndStepLogging() {
    console.log('\n🧪 测试3: 流程和步骤日志');
    console.log('================================');
    
    try {
        const logger = new Logger({
            level: 'info',
            logDir: path.join(projectRoot, 'test-logs'),
            enableConsole: false,
            enableFile: true
        });
        
        // 等待初始化完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟完整流程
        logger.logFlowStart('Test Registration Flow', {
            studentEmail: '<EMAIL>'
        });
        
        // 模拟步骤
        for (let i = 1; i <= 5; i++) {
            logger.logStepStart(`Test Step ${i}`, i, { stepData: `data-${i}` });
            
            // 模拟步骤执行时间
            await new Promise(resolve => setTimeout(resolve, 100));
            
            logger.logStepEnd(`Test Step ${i}`, i, { 
                success: true,
                duration: 100 + i * 10
            });
        }
        
        logger.logFlowEnd('Test Registration Flow', {
            success: true,
            totalSteps: 5,
            duration: 1000
        });
        
        // 等待日志写入
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log('✅ 流程和步骤日志测试成功');
        
        await logger.close();
        return true;
        
    } catch (error) {
        console.error('❌ 流程和步骤日志测试失败:', error.message);
        return false;
    }
}

/**
 * 测试错误日志记录
 */
async function testErrorLogging() {
    console.log('\n🧪 测试4: 错误日志记录');
    console.log('================================');
    
    try {
        const logger = new Logger({
            level: 'info',
            logDir: path.join(projectRoot, 'test-logs'),
            enableConsole: false,
            enableFile: true
        });
        
        // 等待初始化完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟不同类型的错误
        const errors = [
            new Error('网络连接失败'),
            new TypeError('类型错误'),
            new ReferenceError('引用错误')
        ];
        
        for (const error of errors) {
            logger.logError(error, {
                context: 'test',
                step: 'error-test',
                additionalInfo: 'This is a test error'
            });
        }
        
        // 等待日志写入
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log('✅ 错误日志记录测试成功');
        
        await logger.close();
        return true;
        
    } catch (error) {
        console.error('❌ 错误日志记录测试失败:', error.message);
        return false;
    }
}

/**
 * 测试性能日志记录
 */
async function testPerformanceLogging() {
    console.log('\n🧪 测试5: 性能日志记录');
    console.log('================================');
    
    try {
        const logger = new Logger({
            level: 'info',
            logDir: path.join(projectRoot, 'test-logs'),
            enableConsole: false,
            enableFile: true
        });
        
        // 等待初始化完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟性能监控
        const operations = [
            { name: 'Excel读取', duration: 1500 },
            { name: '浏览器启动', duration: 3000 },
            { name: '页面导航', duration: 2000 },
            { name: '表单填写', duration: 1000 },
            { name: '邮件验证', duration: 5000 }
        ];
        
        for (const op of operations) {
            logger.logPerformance(op.name, op.duration, {
                category: 'performance-test',
                threshold: 2000
            });
        }
        
        // 记录用户操作
        logger.logUserAction('click', '#submit-button', { x: 100, y: 200 });
        logger.logUserAction('fill', '#email-input', { value: '<EMAIL>' });
        
        // 记录网络请求
        logger.logNetworkRequest('POST', 'https://api.example.com/register', 200, 1500, {
            requestSize: 1024,
            responseSize: 512
        });
        
        // 等待日志写入
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log('✅ 性能日志记录测试成功');
        
        await logger.close();
        return true;
        
    } catch (error) {
        console.error('❌ 性能日志记录测试失败:', error.message);
        return false;
    }
}

/**
 * 测试日志分析功能
 */
async function testLogAnalysis() {
    console.log('\n🧪 测试6: 日志分析功能');
    console.log('================================');
    
    try {
        const analyzer = new LogAnalyzer({
            logDir: path.join(projectRoot, 'test-logs'),
            outputDir: path.join(projectRoot, 'test-reports')
        });
        
        // 生成快速报告
        console.log('📊 生成快速分析报告...');
        await analyzer.generateQuickReport();
        
        // 生成完整分析
        console.log('\n📊 生成完整分析报告...');
        const analysis = await analyzer.analyzeAllLogs();
        
        if (analysis) {
            console.log('✅ 日志分析功能测试成功');
            console.log(`📊 分析了 ${analysis.summary.totalLogs} 条日志记录`);
            console.log(`🔄 发现 ${analysis.summary.sessions} 个会话`);
            console.log(`❌ 发现 ${analysis.summary.errors} 个错误`);
        } else {
            console.log('⚠️ 未找到日志数据进行分析');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ 日志分析功能测试失败:', error.message);
        return false;
    }
}

/**
 * 测试日志导出功能
 */
async function testLogExport() {
    console.log('\n🧪 测试7: 日志导出功能');
    console.log('================================');
    
    try {
        const logger = new Logger({
            logDir: path.join(projectRoot, 'test-logs'),
            enableConsole: false,
            enableFile: false
        });
        
        const outputDir = path.join(projectRoot, 'test-exports');
        await fs.ensureDir(outputDir);
        
        // 测试JSON导出
        const jsonPath = path.join(outputDir, 'test-export.json');
        const jsonCount = await logger.exportLogs(jsonPath, 'json');
        
        // 测试CSV导出
        const csvPath = path.join(outputDir, 'test-export.csv');
        const csvCount = await logger.exportLogs(csvPath, 'csv');
        
        console.log(`✅ JSON导出: ${jsonCount} 条记录`);
        console.log(`✅ CSV导出: ${csvCount} 条记录`);
        
        if (jsonCount > 0 || csvCount > 0) {
            console.log('✅ 日志导出功能测试成功');
        } else {
            console.log('⚠️ 没有数据可导出');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ 日志导出功能测试失败:', error.message);
        return false;
    }
}

/**
 * 清理测试文件
 */
async function cleanupTestFiles() {
    try {
        const testDirs = [
            path.join(projectRoot, 'test-logs'),
            path.join(projectRoot, 'test-reports'),
            path.join(projectRoot, 'test-exports')
        ];
        
        for (const dir of testDirs) {
            if (await fs.pathExists(dir)) {
                await fs.remove(dir);
                console.log(`🗑️ 已清理测试目录: ${dir}`);
            }
        }
        
    } catch (error) {
        console.error('⚠️ 清理测试文件失败:', error.message);
    }
}

/**
 * 运行日志系统测试套件
 */
async function runLoggingTests() {
    console.log('🚀 日志系统测试套件');
    console.log('=====================================\n');
    
    const tests = [
        { name: '日志记录器初始化', func: testLoggerInitialization },
        { name: '基本日志记录功能', func: testBasicLogging },
        { name: '流程和步骤日志', func: testFlowAndStepLogging },
        { name: '错误日志记录', func: testErrorLogging },
        { name: '性能日志记录', func: testPerformanceLogging },
        { name: '日志分析功能', func: testLogAnalysis },
        { name: '日志导出功能', func: testLogExport }
    ];
    
    const results = [];
    
    for (const test of tests) {
        try {
            const result = await test.func();
            results.push({ name: test.name, success: result });
        } catch (error) {
            console.error(`❌ ${test.name}测试异常:`, error.message);
            results.push({ name: test.name, success: false, error: error.message });
        }
    }
    
    // 输出测试总结
    console.log('\n📊 日志系统测试结果总结:');
    console.log('=====================================');
    
    results.forEach(result => {
        const status = result.success ? '✅ 通过' : '❌ 失败';
        console.log(`${result.name}: ${status}`);
        if (result.error) {
            console.log(`   错误: ${result.error}`);
        }
    });
    
    const passedTests = results.filter(r => r.success).length;
    const totalTests = results.length;
    
    console.log(`\n总计: ${passedTests}/${totalTests} 个测试通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有日志系统测试通过！');
        console.log('\n💡 日志系统功能:');
        console.log('✅ 多级别日志记录 (info, warn, error, debug)');
        console.log('✅ 分类日志管理 (registration, application, email, browser)');
        console.log('✅ 流程和步骤跟踪');
        console.log('✅ 错误详细记录');
        console.log('✅ 性能监控');
        console.log('✅ 日志分析和报告');
        console.log('✅ 数据导出 (JSON, CSV)');
    } else {
        console.log('⚠️ 部分测试失败，请检查日志系统配置');
    }
    
    // 清理测试文件
    console.log('\n🧹 清理测试文件...');
    await cleanupTestFiles();
    
    return passedTests === totalTests;
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
    runLoggingTests().catch(console.error);
}

export { runLoggingTests };
