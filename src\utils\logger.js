/**
 * 日志记录系统
 * 
 * 功能：
 * 1. 多级别日志记录
 * 2. 文件和控制台输出
 * 3. 结构化日志格式
 * 4. 流程状态跟踪
 * 
 * <AUTHOR> Registration Bot
 */

import winston from 'winston';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(dirname(__dirname));

/**
 * 日志记录器类
 */
export class Logger {
    constructor(config = {}) {
        this.config = {
            level: config.level || 'info',
            logDir: config.logDir || path.join(projectRoot, 'logs'),
            maxFiles: config.maxFiles || 10,
            maxSize: config.maxSize || '10m',
            enableConsole: config.enableConsole !== false,
            enableFile: config.enableFile !== false,
            ...config
        };
        
        this.sessionId = this.generateSessionId();
        this.startTime = new Date();
        this.loggers = {};
        
        this.initializeLoggers();
    }

    /**
     * 初始化日志记录器
     */
    async initializeLoggers() {
        try {
            // 确保日志目录存在
            await fs.ensureDir(this.config.logDir);
            
            // 创建主日志记录器
            this.loggers.main = this.createLogger('main');
            
            // 创建专用日志记录器
            this.loggers.registration = this.createLogger('registration');
            this.loggers.application = this.createLogger('application');
            this.loggers.email = this.createLogger('email');
            this.loggers.browser = this.createLogger('browser');
            this.loggers.error = this.createLogger('error');
            
            // 记录会话开始
            this.info('Logger initialized', {
                sessionId: this.sessionId,
                startTime: this.startTime,
                config: this.config
            });
            
        } catch (error) {
            console.error('Failed to initialize logger:', error);
        }
    }

    /**
     * 创建Winston日志记录器
     */
    createLogger(category) {
        const transports = [];
        
        // 控制台输出
        if (this.config.enableConsole) {
            transports.push(
                new winston.transports.Console({
                    format: winston.format.combine(
                        winston.format.colorize(),
                        winston.format.timestamp(),
                        winston.format.printf(({ timestamp, level, message, ...meta }) => {
                            const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
                            return `${timestamp} [${category.toUpperCase()}] ${level}: ${message} ${metaStr}`;
                        })
                    )
                })
            );
        }
        
        // 文件输出
        if (this.config.enableFile) {
            // 主日志文件
            transports.push(
                new winston.transports.File({
                    filename: path.join(this.config.logDir, `${category}.log`),
                    format: winston.format.combine(
                        winston.format.timestamp(),
                        winston.format.json()
                    ),
                    maxsize: this.config.maxSize,
                    maxFiles: this.config.maxFiles
                })
            );
            
            // 错误日志文件
            if (category !== 'error') {
                transports.push(
                    new winston.transports.File({
                        filename: path.join(this.config.logDir, 'error.log'),
                        level: 'error',
                        format: winston.format.combine(
                            winston.format.timestamp(),
                            winston.format.json()
                        ),
                        maxsize: this.config.maxSize,
                        maxFiles: this.config.maxFiles
                    })
                );
            }
        }
        
        return winston.createLogger({
            level: this.config.level,
            transports,
            defaultMeta: {
                sessionId: this.sessionId,
                category
            }
        });
    }

    /**
     * 生成会话ID
     */
    generateSessionId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        return `${timestamp}-${random}`;
    }

    /**
     * 记录信息级别日志
     */
    info(message, meta = {}, category = 'main') {
        this.log('info', message, meta, category);
    }

    /**
     * 记录警告级别日志
     */
    warn(message, meta = {}, category = 'main') {
        this.log('warn', message, meta, category);
    }

    /**
     * 记录错误级别日志
     */
    error(message, meta = {}, category = 'main') {
        this.log('error', message, meta, category);
    }

    /**
     * 记录调试级别日志
     */
    debug(message, meta = {}, category = 'main') {
        this.log('debug', message, meta, category);
    }

    /**
     * 通用日志记录方法
     */
    log(level, message, meta = {}, category = 'main') {
        try {
            const logger = this.loggers[category] || this.loggers.main;
            
            const logData = {
                timestamp: new Date().toISOString(),
                sessionId: this.sessionId,
                category,
                level,
                message,
                ...meta
            };
            
            logger.log(level, message, logData);
            
        } catch (error) {
            console.error('Logging failed:', error);
        }
    }

    /**
     * 记录流程开始
     */
    logFlowStart(flowName, data = {}) {
        this.info(`Flow started: ${flowName}`, {
            flowName,
            startTime: new Date().toISOString(),
            ...data
        }, 'main');
    }

    /**
     * 记录流程结束
     */
    logFlowEnd(flowName, result = {}) {
        this.info(`Flow completed: ${flowName}`, {
            flowName,
            endTime: new Date().toISOString(),
            duration: Date.now() - this.startTime.getTime(),
            ...result
        }, 'main');
    }

    /**
     * 记录步骤开始
     */
    logStepStart(stepName, stepNumber = null, data = {}) {
        this.info(`Step started: ${stepName}`, {
            stepName,
            stepNumber,
            startTime: new Date().toISOString(),
            ...data
        });
    }

    /**
     * 记录步骤结束
     */
    logStepEnd(stepName, stepNumber = null, result = {}) {
        this.info(`Step completed: ${stepName}`, {
            stepName,
            stepNumber,
            endTime: new Date().toISOString(),
            ...result
        });
    }

    /**
     * 记录注册相关日志
     */
    logRegistration(level, message, meta = {}) {
        this.log(level, message, meta, 'registration');
    }

    /**
     * 记录申请相关日志
     */
    logApplication(level, message, meta = {}) {
        this.log(level, message, meta, 'application');
    }

    /**
     * 记录邮件相关日志
     */
    logEmail(level, message, meta = {}) {
        this.log(level, message, meta, 'email');
    }

    /**
     * 记录浏览器相关日志
     */
    logBrowser(level, message, meta = {}) {
        this.log(level, message, meta, 'browser');
    }

    /**
     * 记录错误信息
     */
    logError(error, context = {}) {
        const errorData = {
            errorMessage: error.message,
            errorStack: error.stack,
            errorName: error.name,
            timestamp: new Date().toISOString(),
            ...context
        };
        
        this.log('error', `Error occurred: ${error.message}`, errorData, 'error');
    }

    /**
     * 记录性能指标
     */
    logPerformance(operation, duration, meta = {}) {
        this.info(`Performance: ${operation}`, {
            operation,
            duration,
            timestamp: new Date().toISOString(),
            ...meta
        });
    }

    /**
     * 记录用户操作
     */
    logUserAction(action, target, data = {}) {
        this.info(`User action: ${action}`, {
            action,
            target,
            timestamp: new Date().toISOString(),
            ...data
        }, 'browser');
    }

    /**
     * 记录数据处理
     */
    logDataProcessing(operation, data = {}) {
        this.info(`Data processing: ${operation}`, {
            operation,
            timestamp: new Date().toISOString(),
            ...data
        });
    }

    /**
     * 记录网络请求
     */
    logNetworkRequest(method, url, status, duration, data = {}) {
        this.info(`Network request: ${method} ${url}`, {
            method,
            url,
            status,
            duration,
            timestamp: new Date().toISOString(),
            ...data
        });
    }

    /**
     * 创建子日志记录器
     */
    createChildLogger(name, additionalMeta = {}) {
        return {
            info: (message, meta = {}) => this.info(message, { ...additionalMeta, ...meta }, name),
            warn: (message, meta = {}) => this.warn(message, { ...additionalMeta, ...meta }, name),
            error: (message, meta = {}) => this.error(message, { ...additionalMeta, ...meta }, name),
            debug: (message, meta = {}) => this.debug(message, { ...additionalMeta, ...meta }, name)
        };
    }

    /**
     * 获取日志统计信息
     */
    async getLogStats() {
        try {
            const stats = {
                sessionId: this.sessionId,
                startTime: this.startTime,
                duration: Date.now() - this.startTime.getTime(),
                logFiles: []
            };
            
            // 获取日志文件信息
            const files = await fs.readdir(this.config.logDir);
            for (const file of files) {
                if (file.endsWith('.log')) {
                    const filePath = path.join(this.config.logDir, file);
                    const fileStat = await fs.stat(filePath);
                    stats.logFiles.push({
                        name: file,
                        size: fileStat.size,
                        modified: fileStat.mtime
                    });
                }
            }
            
            return stats;
            
        } catch (error) {
            this.logError(error, { operation: 'getLogStats' });
            return null;
        }
    }

    /**
     * 清理旧日志文件
     */
    async cleanupOldLogs(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7天
        try {
            const files = await fs.readdir(this.config.logDir);
            const now = Date.now();
            
            for (const file of files) {
                if (file.endsWith('.log')) {
                    const filePath = path.join(this.config.logDir, file);
                    const fileStat = await fs.stat(filePath);
                    
                    if (now - fileStat.mtime.getTime() > maxAge) {
                        await fs.remove(filePath);
                        this.info(`Cleaned up old log file: ${file}`);
                    }
                }
            }
            
        } catch (error) {
            this.logError(error, { operation: 'cleanupOldLogs' });
        }
    }

    /**
     * 导出日志数据
     */
    async exportLogs(outputPath, format = 'json') {
        try {
            const logs = [];
            const files = await fs.readdir(this.config.logDir);
            
            for (const file of files) {
                if (file.endsWith('.log')) {
                    const filePath = path.join(this.config.logDir, file);
                    const content = await fs.readFile(filePath, 'utf8');
                    
                    // 解析JSON格式的日志
                    const lines = content.split('\n').filter(line => line.trim());
                    for (const line of lines) {
                        try {
                            const logEntry = JSON.parse(line);
                            logs.push(logEntry);
                        } catch (e) {
                            // 跳过无法解析的行
                        }
                    }
                }
            }
            
            // 按时间排序
            logs.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
            
            // 导出数据
            if (format === 'json') {
                await fs.writeFile(outputPath, JSON.stringify(logs, null, 2));
            } else if (format === 'csv') {
                // 简单的CSV导出
                const csvContent = this.convertToCSV(logs);
                await fs.writeFile(outputPath, csvContent);
            }
            
            this.info(`Logs exported to: ${outputPath}`, { format, count: logs.length });
            return logs.length;
            
        } catch (error) {
            this.logError(error, { operation: 'exportLogs' });
            return 0;
        }
    }

    /**
     * 转换为CSV格式
     */
    convertToCSV(logs) {
        if (logs.length === 0) return '';
        
        const headers = ['timestamp', 'level', 'category', 'message', 'sessionId'];
        const csvLines = [headers.join(',')];
        
        for (const log of logs) {
            const row = headers.map(header => {
                const value = log[header] || '';
                return `"${value.toString().replace(/"/g, '""')}"`;
            });
            csvLines.push(row.join(','));
        }
        
        return csvLines.join('\n');
    }

    /**
     * 关闭日志记录器
     */
    async close() {
        try {
            this.info('Logger shutting down', {
                sessionId: this.sessionId,
                duration: Date.now() - this.startTime.getTime()
            });
            
            // 关闭所有Winston日志记录器
            for (const logger of Object.values(this.loggers)) {
                logger.close();
            }
            
        } catch (error) {
            console.error('Failed to close logger:', error);
        }
    }
}
