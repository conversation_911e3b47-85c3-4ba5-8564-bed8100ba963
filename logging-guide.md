# UNR自动化日志系统指南

## 系统概述

UNR自动化系统集成了完善的日志记录和分析功能，提供全方位的运行状态监控、错误追踪和性能分析。

### 核心功能
- **多级别日志记录** - info, warn, error, debug
- **分类日志管理** - 按功能模块分类记录
- **流程状态跟踪** - 完整的流程和步骤监控
- **错误详细记录** - 包含堆栈信息的错误日志
- **性能监控** - 操作耗时和性能指标
- **日志分析** - 自动生成统计报告
- **数据导出** - 支持JSON和CSV格式

## 日志架构

### 日志分类

```
logs/
├── main.log          # 主要流程日志
├── registration.log  # 注册流程专用日志
├── application.log   # 申请流程专用日志
├── email.log         # 邮件处理日志
├── browser.log       # 浏览器操作日志
└── error.log         # 错误汇总日志
```

### 日志级别

| 级别 | 用途 | 示例 |
|------|------|------|
| **info** | 正常信息记录 | 流程开始、步骤完成 |
| **warn** | 警告信息 | 重试操作、非关键错误 |
| **error** | 错误信息 | 流程失败、异常情况 |
| **debug** | 调试信息 | 详细的执行过程 |

## 使用方法

### 基本日志记录

```javascript
import { Logger } from './utils/logger.js';

// 创建日志记录器
const logger = new Logger({
    level: 'info',
    enableConsole: true,
    enableFile: true
});

// 记录不同级别的日志
logger.info('操作成功', { userId: 123 });
logger.warn('重试操作', { attempt: 2 });
logger.error('操作失败', { error: 'Network timeout' });
logger.debug('调试信息', { data: {...} });
```

### 分类日志记录

```javascript
// 注册流程日志
logger.logRegistration('info', '开始注册流程', {
    email: '<EMAIL>'
});

// 申请流程日志
logger.logApplication('info', '申请步骤完成', {
    step: 3,
    stepName: 'Personal Background'
});

// 邮件处理日志
logger.logEmail('info', '邮件发送成功', {
    recipient: '<EMAIL>',
    subject: 'Verification Code'
});

// 浏览器操作日志
logger.logBrowser('info', '页面导航完成', {
    url: 'https://example.com',
    loadTime: 2000
});
```

### 流程和步骤跟踪

```javascript
// 记录流程开始
logger.logFlowStart('Registration Flow', {
    studentEmail: '<EMAIL>'
});

// 记录步骤
logger.logStepStart('Fill Registration Form', 1);
// ... 执行步骤 ...
logger.logStepEnd('Fill Registration Form', 1, {
    success: true,
    duration: 5000
});

// 记录流程结束
logger.logFlowEnd('Registration Flow', {
    success: true,
    totalSteps: 5,
    duration: 30000
});
```

### 错误记录

```javascript
try {
    // 执行操作
    await someOperation();
} catch (error) {
    // 记录详细错误信息
    logger.logError(error, {
        operation: 'someOperation',
        context: 'registration',
        additionalData: {...}
    });
}
```

### 性能监控

```javascript
const startTime = Date.now();
await performOperation();
const duration = Date.now() - startTime;

// 记录性能数据
logger.logPerformance('Database Query', duration, {
    query: 'SELECT * FROM users',
    recordCount: 100
});

// 记录用户操作
logger.logUserAction('click', '#submit-button', {
    x: 100, y: 200
});

// 记录网络请求
logger.logNetworkRequest('POST', '/api/register', 200, 1500, {
    requestSize: 1024,
    responseSize: 512
});
```

## 日志管理工具

### 命令行工具

系统提供了强大的命令行工具来管理日志：

```bash
# 查看日志状态
npm run logs:status

# 快速分析报告
npm run logs:quick

# 完整分析报告
npm run logs:analyze

# 清理旧日志
npm run logs:clean

# 导出日志数据
npm run logs:export

# 实时查看日志
npm run logs:tail
```

### 详细命令说明

#### 1. 日志状态检查
```bash
npm run logs:status
```
显示：
- 日志目录状态
- 日志文件列表
- 文件大小和修改时间
- 快速统计信息

#### 2. 快速分析
```bash
npm run logs:quick
```
提供：
- 总日志数量
- 错误和警告统计
- 日志级别分布
- 分类分布
- 最近错误列表

#### 3. 完整分析
```bash
npm run logs:analyze
```
生成：
- 详细的HTML分析报告
- JSON格式的原始数据
- 性能分析
- 错误分析
- 流程执行统计

#### 4. 清理旧日志
```bash
# 清理7天前的日志（默认）
npm run logs:clean

# 清理3天前的日志
node src/tools/logManager.js clean --days 3
```

#### 5. 导出日志数据
```bash
# 导出为JSON格式
npm run logs:export

# 导出为CSV格式
node src/tools/logManager.js export --format csv

# 指定输出文件
node src/tools/logManager.js export --output my-logs.json
```

#### 6. 实时查看日志
```bash
# 查看最新50行日志
npm run logs:tail

# 查看最新100行日志
node src/tools/logManager.js tail --lines 100
```

## 日志分析

### 自动分析报告

系统会自动生成包含以下内容的分析报告：

#### 1. 总体统计
- 总日志数量
- 会话数量
- 错误和警告统计
- 时间范围

#### 2. 流程执行情况
- 流程启动次数
- 完成次数
- 成功率
- 失败原因分析

#### 3. 步骤执行分析
- 各步骤执行统计
- 成功率分析
- 常见失败点

#### 4. 错误分析
- 错误分类统计
- 错误类型分布
- 错误发生步骤
- 错误详细信息

#### 5. 性能分析
- 流程执行时间
- 步骤耗时分析
- 性能趋势
- 瓶颈识别

#### 6. 时间线分析
- 按小时的日志分布
- 活动高峰时段
- 错误发生时间模式

### 报告格式

#### HTML报告
- 可视化图表
- 交互式数据展示
- 详细的统计表格
- 美观的界面设计

#### JSON报告
- 结构化数据
- 便于程序处理
- 完整的原始信息
- 支持二次分析

#### CSV导出
- 表格格式数据
- 便于Excel处理
- 适合数据分析
- 支持筛选排序

## 配置选项

### 日志记录器配置

```javascript
const logger = new Logger({
    level: 'info',              // 日志级别
    logDir: './logs',           // 日志目录
    maxFiles: 10,               // 最大文件数
    maxSize: '10m',             // 最大文件大小
    enableConsole: true,        // 启用控制台输出
    enableFile: true            // 启用文件输出
});
```

### 分析器配置

```javascript
const analyzer = new LogAnalyzer({
    logDir: './logs',           // 日志目录
    outputDir: './reports'      // 报告输出目录
});
```

## 最佳实践

### 1. 日志记录原则

**记录关键信息**
```javascript
// ✅ 好的做法
logger.info('用户注册成功', {
    email: user.email,
    registrationTime: new Date(),
    source: 'web'
});

// ❌ 避免的做法
logger.info('成功');
```

**使用适当的日志级别**
```javascript
// ✅ 正确使用级别
logger.info('正常操作完成');
logger.warn('重试操作，可能的网络问题');
logger.error('操作失败，需要人工干预');
logger.debug('详细的调试信息');
```

**包含上下文信息**
```javascript
// ✅ 包含上下文
logger.logError(error, {
    operation: 'userRegistration',
    step: 'emailVerification',
    userId: user.id,
    timestamp: new Date()
});
```

### 2. 性能监控

**监控关键操作**
```javascript
const startTime = Date.now();
await criticalOperation();
const duration = Date.now() - startTime;

logger.logPerformance('Critical Operation', duration, {
    threshold: 5000,  // 性能阈值
    category: 'database'
});
```

**设置性能阈值**
```javascript
if (duration > 5000) {
    logger.warn('操作耗时过长', {
        operation: 'database_query',
        duration,
        threshold: 5000
    });
}
```

### 3. 错误处理

**详细的错误记录**
```javascript
try {
    await riskyOperation();
} catch (error) {
    logger.logError(error, {
        operation: 'riskyOperation',
        parameters: {...},
        context: 'user_registration',
        recoveryAction: 'retry_with_backoff'
    });
    
    // 记录恢复尝试
    logger.info('开始错误恢复', {
        errorType: error.name,
        recoveryStrategy: 'exponential_backoff'
    });
}
```

### 4. 日志维护

**定期清理**
```bash
# 设置定期清理任务
# 每周清理7天前的日志
0 2 * * 0 cd /path/to/project && npm run logs:clean
```

**监控日志大小**
```javascript
// 检查日志文件大小
const stats = await logger.getLogStats();
if (stats.totalSize > 100 * 1024 * 1024) { // 100MB
    logger.warn('日志文件过大', {
        totalSize: stats.totalSize,
        recommendation: 'consider_cleanup'
    });
}
```

## 故障排除

### 常见问题

#### 1. 日志文件未生成
**问题**: 运行程序后没有生成日志文件
**解决方案**:
```bash
# 检查日志目录权限
ls -la logs/

# 检查配置
node -e "import('./src/utils/logger.js').then(m => console.log('Logger imported successfully'))"
```

#### 2. 日志分析失败
**问题**: 分析命令报错
**解决方案**:
```bash
# 检查日志文件格式
npm run logs:status

# 手动验证JSON格式
head -n 5 logs/main.log | jq .
```

#### 3. 性能问题
**问题**: 日志记录影响性能
**解决方案**:
```javascript
// 使用异步日志记录
const logger = new Logger({
    level: 'info',
    enableConsole: false,  // 禁用控制台输出
    enableFile: true
});
```

### 调试技巧

#### 1. 启用调试模式
```bash
# 启用详细日志
DEBUG=* npm start
```

#### 2. 查看特定分类日志
```bash
# 只查看错误日志
tail -f logs/error.log

# 查看注册流程日志
tail -f logs/registration.log
```

#### 3. 实时监控
```bash
# 实时查看所有日志
tail -f logs/*.log

# 过滤特定内容
tail -f logs/main.log | grep "ERROR"
```

## 扩展功能

### 自定义日志记录器

```javascript
// 创建专用日志记录器
const customLogger = logger.createChildLogger('custom', {
    module: 'payment',
    version: '1.0.0'
});

customLogger.info('支付处理开始', {
    orderId: '12345',
    amount: 99.99
});
```

### 集成外部监控

```javascript
// 集成到监控系统
logger.on('error', (error) => {
    // 发送到外部监控系统
    monitoringService.reportError(error);
});
```

### 自定义分析

```javascript
// 扩展分析功能
class CustomAnalyzer extends LogAnalyzer {
    analyzePayments(logs) {
        // 自定义支付分析逻辑
        return paymentAnalysis;
    }
}
```

## 总结

UNR自动化日志系统提供了完整的日志记录、分析和管理功能：

✅ **全面记录** - 覆盖所有关键操作和状态
✅ **智能分析** - 自动生成详细的分析报告
✅ **便捷管理** - 丰富的命令行工具
✅ **性能监控** - 实时性能指标跟踪
✅ **错误追踪** - 详细的错误信息和上下文
✅ **数据导出** - 支持多种格式的数据导出

通过合理使用日志系统，可以大大提高系统的可维护性、可调试性和可监控性。
