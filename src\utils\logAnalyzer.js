/**
 * 日志分析工具
 * 
 * 功能：
 * 1. 分析日志文件
 * 2. 生成统计报告
 * 3. 性能分析
 * 4. 错误分析
 * 
 * <AUTHOR> Registration Bot
 */

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(dirname(__dirname));

/**
 * 日志分析器类
 */
export class LogAnalyzer {
    constructor(config = {}) {
        this.config = {
            logDir: config.logDir || path.join(projectRoot, 'logs'),
            outputDir: config.outputDir || path.join(projectRoot, 'reports'),
            ...config
        };
    }

    /**
     * 分析所有日志文件
     */
    async analyzeAllLogs() {
        try {
            console.log('📊 开始分析日志文件...');
            
            // 确保输出目录存在
            await fs.ensureDir(this.config.outputDir);
            
            // 读取所有日志文件
            const logs = await this.readAllLogFiles();
            
            if (logs.length === 0) {
                console.log('⚠️ 未找到日志文件');
                return null;
            }
            
            console.log(`📋 共找到 ${logs.length} 条日志记录`);
            
            // 生成各种分析报告
            const analysis = {
                summary: this.generateSummary(logs),
                performance: this.analyzePerformance(logs),
                errors: this.analyzeErrors(logs),
                flows: this.analyzeFlows(logs),
                steps: this.analyzeSteps(logs),
                categories: this.analyzeCategories(logs),
                timeline: this.generateTimeline(logs)
            };
            
            // 保存分析报告
            await this.saveAnalysisReport(analysis);
            
            console.log('✅ 日志分析完成');
            return analysis;
            
        } catch (error) {
            console.error('❌ 日志分析失败:', error.message);
            throw error;
        }
    }

    /**
     * 读取所有日志文件
     */
    async readAllLogFiles() {
        const logs = [];
        
        try {
            const files = await fs.readdir(this.config.logDir);
            
            for (const file of files) {
                if (file.endsWith('.log')) {
                    const filePath = path.join(this.config.logDir, file);
                    const content = await fs.readFile(filePath, 'utf8');
                    
                    // 解析JSON格式的日志
                    const lines = content.split('\n').filter(line => line.trim());
                    for (const line of lines) {
                        try {
                            const logEntry = JSON.parse(line);
                            logEntry.sourceFile = file;
                            logs.push(logEntry);
                        } catch (e) {
                            // 跳过无法解析的行
                        }
                    }
                }
            }
            
            // 按时间排序
            logs.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
            
        } catch (error) {
            console.error('读取日志文件失败:', error.message);
        }
        
        return logs;
    }

    /**
     * 生成摘要统计
     */
    generateSummary(logs) {
        const summary = {
            totalLogs: logs.length,
            timeRange: {
                start: logs[0]?.timestamp,
                end: logs[logs.length - 1]?.timestamp
            },
            levels: {},
            categories: {},
            sessions: new Set(),
            errors: 0,
            warnings: 0
        };
        
        for (const log of logs) {
            // 统计日志级别
            summary.levels[log.level] = (summary.levels[log.level] || 0) + 1;
            
            // 统计分类
            summary.categories[log.category] = (summary.categories[log.category] || 0) + 1;
            
            // 统计会话
            if (log.sessionId) {
                summary.sessions.add(log.sessionId);
            }
            
            // 统计错误和警告
            if (log.level === 'error') summary.errors++;
            if (log.level === 'warn') summary.warnings++;
        }
        
        summary.sessions = summary.sessions.size;
        
        return summary;
    }

    /**
     * 分析性能数据
     */
    analyzePerformance(logs) {
        const performance = {
            flows: {},
            steps: {},
            operations: {}
        };
        
        // 分析流程性能
        const flowStarts = logs.filter(log => log.message && log.message.includes('Flow started'));
        const flowEnds = logs.filter(log => log.message && log.message.includes('Flow completed'));
        
        for (const start of flowStarts) {
            const flowName = start.flowName;
            const end = flowEnds.find(e => e.flowName === flowName && e.sessionId === start.sessionId);
            
            if (end && end.duration) {
                if (!performance.flows[flowName]) {
                    performance.flows[flowName] = [];
                }
                performance.flows[flowName].push(end.duration);
            }
        }
        
        // 计算平均值
        for (const [flowName, durations] of Object.entries(performance.flows)) {
            const avg = durations.reduce((a, b) => a + b, 0) / durations.length;
            const min = Math.min(...durations);
            const max = Math.max(...durations);
            
            performance.flows[flowName] = {
                count: durations.length,
                average: Math.round(avg),
                min,
                max,
                durations
            };
        }
        
        return performance;
    }

    /**
     * 分析错误信息
     */
    analyzeErrors(logs) {
        const errorLogs = logs.filter(log => log.level === 'error');
        
        const errors = {
            total: errorLogs.length,
            byCategory: {},
            byType: {},
            byStep: {},
            details: []
        };
        
        for (const error of errorLogs) {
            // 按分类统计
            errors.byCategory[error.category] = (errors.byCategory[error.category] || 0) + 1;
            
            // 按错误类型统计
            if (error.errorName) {
                errors.byType[error.errorName] = (errors.byType[error.errorName] || 0) + 1;
            }
            
            // 按步骤统计
            if (error.step) {
                errors.byStep[error.step] = (errors.byStep[error.step] || 0) + 1;
            }
            
            // 保存错误详情
            errors.details.push({
                timestamp: error.timestamp,
                category: error.category,
                message: error.errorMessage || error.message,
                step: error.step,
                sessionId: error.sessionId
            });
        }
        
        return errors;
    }

    /**
     * 分析流程执行情况
     */
    analyzeFlows(logs) {
        const flows = {};
        
        const flowLogs = logs.filter(log => 
            log.message && (
                log.message.includes('Flow started') || 
                log.message.includes('Flow completed')
            )
        );
        
        for (const log of flowLogs) {
            const flowName = log.flowName;
            if (!flows[flowName]) {
                flows[flowName] = {
                    started: 0,
                    completed: 0,
                    success: 0,
                    failed: 0
                };
            }
            
            if (log.message.includes('started')) {
                flows[flowName].started++;
            } else if (log.message.includes('completed')) {
                flows[flowName].completed++;
                
                if (log.registrationSuccess !== false && log.applicationSuccess !== false) {
                    flows[flowName].success++;
                } else {
                    flows[flowName].failed++;
                }
            }
        }
        
        return flows;
    }

    /**
     * 分析步骤执行情况
     */
    analyzeSteps(logs) {
        const steps = {};
        
        const stepLogs = logs.filter(log => 
            log.message && (
                log.message.includes('Step started') || 
                log.message.includes('Step completed')
            )
        );
        
        for (const log of stepLogs) {
            const stepName = log.stepName;
            if (!steps[stepName]) {
                steps[stepName] = {
                    started: 0,
                    completed: 0,
                    success: 0,
                    failed: 0
                };
            }
            
            if (log.message.includes('started')) {
                steps[stepName].started++;
            } else if (log.message.includes('completed')) {
                steps[stepName].completed++;
                
                if (log.success !== false) {
                    steps[stepName].success++;
                } else {
                    steps[stepName].failed++;
                }
            }
        }
        
        return steps;
    }

    /**
     * 分析日志分类
     */
    analyzeCategories(logs) {
        const categories = {};
        
        for (const log of logs) {
            const category = log.category || 'unknown';
            
            if (!categories[category]) {
                categories[category] = {
                    total: 0,
                    info: 0,
                    warn: 0,
                    error: 0,
                    debug: 0
                };
            }
            
            categories[category].total++;
            categories[category][log.level] = (categories[category][log.level] || 0) + 1;
        }
        
        return categories;
    }

    /**
     * 生成时间线
     */
    generateTimeline(logs) {
        const timeline = [];
        
        // 按小时分组
        const hourlyStats = {};
        
        for (const log of logs) {
            const hour = new Date(log.timestamp).toISOString().substring(0, 13);
            
            if (!hourlyStats[hour]) {
                hourlyStats[hour] = {
                    hour,
                    total: 0,
                    info: 0,
                    warn: 0,
                    error: 0,
                    debug: 0
                };
            }
            
            hourlyStats[hour].total++;
            hourlyStats[hour][log.level]++;
        }
        
        return Object.values(hourlyStats).sort((a, b) => a.hour.localeCompare(b.hour));
    }

    /**
     * 保存分析报告
     */
    async saveAnalysisReport(analysis) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            
            // 保存JSON格式报告
            const jsonPath = path.join(this.config.outputDir, `log-analysis-${timestamp}.json`);
            await fs.writeFile(jsonPath, JSON.stringify(analysis, null, 2));
            
            // 生成HTML报告
            const htmlPath = path.join(this.config.outputDir, `log-analysis-${timestamp}.html`);
            const htmlContent = this.generateHTMLReport(analysis);
            await fs.writeFile(htmlPath, htmlContent);
            
            console.log(`📊 分析报告已保存:`);
            console.log(`   JSON: ${jsonPath}`);
            console.log(`   HTML: ${htmlPath}`);
            
        } catch (error) {
            console.error('保存分析报告失败:', error.message);
        }
    }

    /**
     * 生成HTML报告
     */
    generateHTMLReport(analysis) {
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UNR自动化日志分析报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .stat-card { background: #f9f9f9; padding: 15px; border-radius: 5px; text-align: center; }
        .error { color: #d32f2f; }
        .warning { color: #f57c00; }
        .success { color: #388e3c; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 UNR自动化日志分析报告</h1>
        <p>生成时间: ${new Date().toLocaleString()}</p>
    </div>

    <div class="section">
        <h2>📊 总体统计</h2>
        <div class="stats">
            <div class="stat-card">
                <h3>总日志数</h3>
                <p>${analysis.summary.totalLogs}</p>
            </div>
            <div class="stat-card">
                <h3>会话数</h3>
                <p>${analysis.summary.sessions}</p>
            </div>
            <div class="stat-card error">
                <h3>错误数</h3>
                <p>${analysis.summary.errors}</p>
            </div>
            <div class="stat-card warning">
                <h3>警告数</h3>
                <p>${analysis.summary.warnings}</p>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🔄 流程执行情况</h2>
        <table>
            <tr><th>流程名称</th><th>启动次数</th><th>完成次数</th><th>成功次数</th><th>失败次数</th></tr>
            ${Object.entries(analysis.flows).map(([name, stats]) => `
                <tr>
                    <td>${name}</td>
                    <td>${stats.started}</td>
                    <td>${stats.completed}</td>
                    <td class="success">${stats.success}</td>
                    <td class="error">${stats.failed}</td>
                </tr>
            `).join('')}
        </table>
    </div>

    <div class="section">
        <h2>❌ 错误分析</h2>
        <h3>按分类统计</h3>
        <table>
            <tr><th>分类</th><th>错误数量</th></tr>
            ${Object.entries(analysis.errors.byCategory).map(([category, count]) => `
                <tr><td>${category}</td><td class="error">${count}</td></tr>
            `).join('')}
        </table>
    </div>

    <div class="section">
        <h2>⚡ 性能分析</h2>
        <table>
            <tr><th>流程名称</th><th>执行次数</th><th>平均耗时(ms)</th><th>最短耗时(ms)</th><th>最长耗时(ms)</th></tr>
            ${Object.entries(analysis.performance.flows).map(([name, stats]) => `
                <tr>
                    <td>${name}</td>
                    <td>${stats.count}</td>
                    <td>${stats.average}</td>
                    <td>${stats.min}</td>
                    <td>${stats.max}</td>
                </tr>
            `).join('')}
        </table>
    </div>
</body>
</html>`;
    }

    /**
     * 生成简要报告
     */
    async generateQuickReport() {
        try {
            const logs = await this.readAllLogFiles();
            
            if (logs.length === 0) {
                console.log('⚠️ 未找到日志文件');
                return;
            }
            
            const summary = this.generateSummary(logs);
            const errors = this.analyzeErrors(logs);
            
            console.log('\n📊 日志快速分析报告');
            console.log('=====================================');
            console.log(`📋 总日志数: ${summary.totalLogs}`);
            console.log(`🔄 会话数: ${summary.sessions}`);
            console.log(`❌ 错误数: ${summary.errors}`);
            console.log(`⚠️ 警告数: ${summary.warnings}`);
            
            if (summary.timeRange.start && summary.timeRange.end) {
                console.log(`⏰ 时间范围: ${summary.timeRange.start} ~ ${summary.timeRange.end}`);
            }
            
            console.log('\n📊 日志级别分布:');
            Object.entries(summary.levels).forEach(([level, count]) => {
                console.log(`  ${level}: ${count}`);
            });
            
            console.log('\n📂 分类分布:');
            Object.entries(summary.categories).forEach(([category, count]) => {
                console.log(`  ${category}: ${count}`);
            });
            
            if (errors.total > 0) {
                console.log('\n❌ 最近错误:');
                errors.details.slice(-5).forEach(error => {
                    console.log(`  ${error.timestamp}: ${error.message}`);
                });
            }
            
        } catch (error) {
            console.error('❌ 生成快速报告失败:', error.message);
        }
    }
}
