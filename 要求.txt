申请页面：
https://admissions.unr.edu/apply/

注册：
https://admissions.unr.edu/account/register

登录：
https://admissions.unr.edu/account/login

1. **Email Integration Module**: Create a function to connect to the provided QQ email account using IMAP protocol with these exact credentials:
   - IMAP_SERVER: imap.qq.com
   - IMAP_PORT: 993
   - IMAP_USER: <EMAIL>
   - IMAP_PASS: pnsbpvwlrvpybiei
   - IMAP_DIR: INBOX
   - IMAP_PROTOCOL: IMAP
   
 注册是pin获取邮件：
 David,

Thank you for registering online with the University of Nevada, Reno.

Use the link below to activate your account:

Authenticate Application

When requested for a PIN, enter the following temporary PIN:

*********

Feel free to contact our Office of Admissions if you need assistance. 

University of Nevada, Reno
(775) 784-4700, opt. 1 | <EMAIL>
   
   
现在进行第一个，访问https://admissions.unr.edu/account/register，进行注册，填写相关信息，从excel表中读取邮箱
，名 ，姓，生日  ，填完后点击Continue按钮，然后读取接下来的页面



首先点击  #start_application_link
检测是否出现 body > div.dialog_host.ui-draggable > div > div > form
在#period中选择 2025 Undergradduate Application,值为ed5a5d2d-5c2e-471d-a98a-c36990b31016就是第一个，然后点确认按钮
body > div.dialog_host.ui-draggable > div > div > form > div.action > button.default


接下来检查#content中#content > table > tbody > tr > td:nth-child(1) > div > a
#content > table > tbody > tr > td:nth-child(2) 是否是<td>In Progress</td>，那么就正式进行申请了

点击#content > table > tbody > tr > td:nth-child(1) > div > a，出现申请框详情，然后点击body > div.dialog_host.ui-draggable > div > div > form > div.action > button.default，，打开申请页面，申请页面的文件为frm，已经存在项目目录下

现在已经可以进入到申请表单填写页面了，进行具体的注册，第一个页面Application Information
selector中	 #form_e25f6bd4-ed77-431f-bf2d-efc8615a675f > div.form_responses
选择这个选项#form_e25f6bd4-ed77-431f-bf2d-efc8615a675f_2

点击后出现另外一个选择框 点击这个元素#form_edb6bd5a-1c5e-457d-b4b6-97314f7840c2_2

点击后出现另外一个选择框 点击这个元素#form_39e5db3d-2a77-4f3c-ba9c-54bebe9528bd_2
点击后出现另外一个选择框 点击这个元素#form_60070daf-0765-4d0f-bd2f-cc79ed50c4ce_2

点击后出现另外一个选择框 点击这个元素#form_83fc6010-88e2-40fd-a456-7b3b92877248_3  data-text="Nondegree"

点击后出现另外一个选择框 点击这个元素#form_6ff0815b-1c44-4b9f-bf2c-cfcb7c030afb_4

最后点击
#main > form > div.action > button  第一个完成，系统会自动保存这些选择的


第二个填写信息为点击#menu > ul > li:nth-child(3) > a 进入到另外一个子页面
然后点击#main > form > div.action > button 完成这个界面为 Nondegree Application Instructions

第三个填写信息为点击#menu > ul > li:nth-child(4) > a 进入到另外一个子页面
点击这个#form_6c9860b5-6412-4e1c-a82b-6dc0bfa77ca7_1
然后点击#main > form > div.action > button 完成这个界面为 Application Term

第四个填写信息为点击#menu > ul > li:nth-child(4) > a 进入到另外一个子页面，这个界面为Personal Background
1.找到#address_1_street这个框，填写test.xlsx中行为完整地址中的街道比如test.xlsx中完整地址信息为Camp Chase Trail, Columbus, OH 43328, United States，那么填写Camp Chase Trail
2.#address_1_city填写为Columbus
3.#address_1_region中选择对应的州OH为州的缩写，那么就选择，Ohio
4.#address_1_postal 填写对应的 43328
5.检查地址元素是否都填好了
6.点击#addresses > tbody:nth-child(5) > tr.address_mailing > td > a
7.#phone 和中填写test.xlsx中列号为电话中的，例如电话(*************
8.#sex中选择test.xlsx中列号为性别中对应的性别
9.#birthplace填写test.xlsx中行为完整地址中的街道中的城市Columbus
10.#birthregion中选择test.xlsx中行为完整地址中的街道中的对应的州
11.#citizenship1 中选择United States
12.#ssn中填写test.xlsx中列号为社会安全号，但是数据要处理一下，比如原来是这中格式的***********，要去掉-，变成247338664 进行填写
13.点击#hispanic_0   点击#race_cd6b1bac-e441-4649-a7d3-a61ccf8c860c
14.最后点击#main > form > div.action > button，完成这个页面的填写

15.有时候地址不对会弹出提示框来，body > div.dialog_host.ui-draggable > div
	然后点击body > div.dialog_host.ui-draggable > div > div.action > button:nth-child(2) 按钮即可

第五个子页面填写信息为Background Continued，参考项目文件Background Continued
1.#form_f0cd62fe-6c76-4421-b049-a27bf3ce1ff3 选择为 English
2.What pronouns do you use? (check one)*下 要选择test.xlsx中性别对应的，比如male选择He/Him,Fanale选择为 She/Her
3.Which of the following best represents how think of your gender? (check one)*中要选择test.xlsx中性别对应的 Man或者  woman
4.Which of the following best represents how think of your sexual orientation? (check one)*选 Asexual
5.最后点击#main > form > div.action > button，完成这个页面的填写

第六个子页面填写信息为Emergency Contact，参考本地文件Emergency Contact
1.填写#form_6a88bc22-daf7-4a6b-b573-3026467437ac First Name*为一个随机的人名
2.填写 #form_f7281ea7-5538-4695-a7c9-615cb6dbb378 Last Name* 为一个随机的人名
3.#form_6ec61e72-6946-4456-a1eb-67c692cbf515_street  填写test.xlsx中行为完整地址中的街道比如test.xlsx中完整地址信息为Camp Chase Trail, Columbus, OH 43328, United States，那么填写Camp Chase Trail
4.#form_6ec61e72-6946-4456-a1eb-67c692cbf515_city  填写test.xlsx中行为完整地址中的街道比如test.xlsx中完整地址信息为，填写为Columbus
5.#form_6ec61e72-6946-4456-a1eb-67c692cbf515_region中选择对应的州OH为州的缩写，那么就选择，Ohio
6.#form_6ec61e72-6946-4456-a1eb-67c692cbf515_postal  填写对应的 43328
7.#form_f1f59cb1-fc93-433a-ab58-4337e8627666  中填写test.xlsx中列号为电话中的，例如电话(*************
8.#form_413476ec-1b40-47e7-80c4-a4a0dfce33f4  随机填写一个gmail.com的邮箱
9.#form_1098cd41-1d4e-4b3c-8111-a527f4647edc 选择为other RelationShip
10.#form_15ac7861-7d72-4b55-a398-65e17f0ab1d8 选择为 English

第七个子页面填写信息为Academic History，参考本地文件Academic History
也是一样要看看已经添加好学校没，添加了的话就进行下一步
1.点击#form_question_eea3d6a0-5b3f-4305-8a55-7a6cf37e55cf > div > table > thead > tr.row_hover > td > a，添加学校
2.会弹出一个form表body > div.dialog_host.ui-draggable > div > form
从test.xlsx中sheet名称为 高中信息中读取学校名称，比如说读取的是Maplewood High School
，那么再#form_898992fd-b0ef-440b-8a1f-ab54dfe744fb框中填写，此时会从网络中下载列表，然后你随机选择一个，点击即可。
3.#form_cf46b0f9-397e-472c-88ec-43b338e10792_m 选择August
#form_cf46b0f9-397e-472c-88ec-43b338e10792_y 选择 2020
#form_6f1f23d2-b19c-475d-93a7-92c1ee45487a_m 选择 July
#form_6f1f23d2-b19c-475d-93a7-92c1ee45487a_y 选择 2024
4.点击#form_d016e597-011e-4c21-be71-9258f5395b47_2 
5.点击body > div.dialog_host.ui-draggable > div > form > div.action > button.default  进行保存
6.等待保存成功后，点击#main > form > div.action > button。完成这个页面

第八个子页面填写信息为Program Selection，参考本地文件Program Selection
1.点击#form_a3f832c6-31fb-478c-bb05-29aa54f7089e_1
2.点击#main > form > div.action > button 完成了

第九个子页面填写信息为Signature，参考本地文件Signature
1. #main > form > p > input 输入框中输入test.xlsx中学生姓名
2.点击#main > form > div > button 进行完成

第十个子页面填写信息为Review
1.没有问题的情况下点击 #main > form > div > button.default 进行提交就行了