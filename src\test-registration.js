/**
 * 注册流程测试脚本
 * 
 * 测试UNR账户注册自动化功能
 * 
 * <AUTHOR> Registration Bot
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { ExcelReader } from './utils/excelReader.js';
import { BrowserController } from './automation/browserController.js';
import { RegistrationFlow } from './automation/registrationFlow.js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 获取项目根目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

/**
 * 测试注册流程组件初始化
 */
async function testRegistrationComponents() {
    console.log('🧪 测试1: 注册流程组件初始化');
    console.log('================================');
    
    let browser = null;
    
    try {
        // 1. 测试Excel读取
        console.log('📊 测试Excel数据读取...');
        const excelPath = join(projectRoot, 'test.xlsx');
        const reader = new ExcelReader(excelPath);
        const studentData = await reader.readStudentData();
        
        console.log('✅ Excel数据读取成功');
        console.log(`👤 学生: ${studentData.firstName} ${studentData.lastName}`);
        console.log(`📧 邮箱: ${studentData.email}`);
        
        // 2. 测试浏览器控制器
        console.log('\n🌐 测试浏览器控制器...');
        browser = new BrowserController({
            headless: true,
            timeout: 15000
        });
        
        await browser.launch();
        console.log('✅ 浏览器启动成功');
        
        // 3. 测试注册流程控制器
        console.log('\n📝 测试注册流程控制器...');
        const registrationFlow = new RegistrationFlow(browser, {
            registerUrl: 'https://admissions.unr.edu/account/register'
        });
        
        console.log('✅ 注册流程控制器创建成功');
        
        return { studentData, browser, registrationFlow };
        
    } catch (error) {
        console.error('❌ 组件初始化测试失败:', error.message);
        if (browser) {
            await browser.close();
        }
        return null;
    }
}

/**
 * 测试注册页面访问
 */
async function testRegistrationPageAccess(browser) {
    console.log('\n🧪 测试2: 注册页面访问');
    console.log('================================');
    
    try {
        const registerUrl = 'https://admissions.unr.edu/account/register';
        
        console.log(`🔗 访问注册页面: ${registerUrl}`);
        await browser.navigateTo(registerUrl, { screenshot: false });
        
        const title = await browser.getPageTitle();
        const url = await browser.getCurrentUrl();
        
        console.log(`📄 页面标题: ${title}`);
        console.log(`🔗 当前URL: ${url}`);
        
        if (url.includes('unr.edu')) {
            console.log('✅ 成功访问UNR网站');
            
            // 检查是否有注册表单
            try {
                await browser.waitForElement('form', { timeout: 10000 });
                console.log('✅ 找到注册表单');
                
                // 截图记录
                await browser.takeScreenshot('test-registration-page');
                
                return true;
            } catch (error) {
                console.log('⚠️ 未找到注册表单，页面结构可能已变化');
                return false;
            }
        } else {
            console.log('❌ 未能访问到UNR网站');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 注册页面访问测试失败:', error.message);
        return false;
    }
}

/**
 * 测试表单元素识别
 */
async function testFormElementRecognition(browser) {
    console.log('\n🧪 测试3: 表单元素识别');
    console.log('================================');
    
    try {
        console.log('🔍 检查表单元素...');
        
        const elementsToCheck = [
            { name: '邮箱输入框', selectors: ['input[name="email"]', 'input[type="email"]', '#email'] },
            { name: '名字输入框', selectors: ['input[name="firstName"]', 'input[name="first_name"]', '#firstName'] },
            { name: '姓氏输入框', selectors: ['input[name="lastName"]', 'input[name="last_name"]', '#lastName'] },
            { name: '密码输入框', selectors: ['input[name="password"]', 'input[type="password"]', '#password'] },
            { name: '提交按钮', selectors: ['button[type="submit"]', 'input[type="submit"]', '#submit'] }
        ];
        
        const foundElements = [];
        const missingElements = [];
        
        for (const element of elementsToCheck) {
            let found = false;
            
            for (const selector of element.selectors) {
                try {
                    await browser.waitForElement(selector, { timeout: 3000 });
                    console.log(`✅ 找到${element.name}: ${selector}`);
                    foundElements.push({ name: element.name, selector });
                    found = true;
                    break;
                } catch (error) {
                    // 继续尝试下一个选择器
                }
            }
            
            if (!found) {
                console.log(`❌ 未找到${element.name}`);
                missingElements.push(element.name);
            }
        }
        
        console.log(`\n📊 元素识别结果:`);
        console.log(`✅ 找到: ${foundElements.length}/${elementsToCheck.length} 个元素`);
        console.log(`❌ 缺失: ${missingElements.length} 个元素`);
        
        if (missingElements.length > 0) {
            console.log(`缺失元素: ${missingElements.join(', ')}`);
        }
        
        return foundElements.length >= 3; // 至少找到3个元素才算成功
        
    } catch (error) {
        console.error('❌ 表单元素识别测试失败:', error.message);
        return false;
    }
}

/**
 * 测试表单填写（模拟）
 */
async function testFormFilling(browser, studentData) {
    console.log('\n🧪 测试4: 表单填写模拟');
    console.log('================================');
    
    try {
        console.log('✏️ 模拟填写表单...');
        
        // 模拟填写邮箱
        const emailSelectors = ['input[name="email"]', 'input[type="email"]', '#email'];
        for (const selector of emailSelectors) {
            try {
                await browser.waitForElement(selector, { timeout: 3000 });
                await browser.fillInput(selector, studentData.email);
                console.log(`✅ 邮箱填写成功: ${studentData.email}`);
                break;
            } catch (error) {
                continue;
            }
        }
        
        // 模拟填写名字
        const firstNameSelectors = ['input[name="firstName"]', 'input[name="first_name"]', '#firstName'];
        for (const selector of firstNameSelectors) {
            try {
                await browser.waitForElement(selector, { timeout: 3000 });
                await browser.fillInput(selector, studentData.firstName);
                console.log(`✅ 名字填写成功: ${studentData.firstName}`);
                break;
            } catch (error) {
                continue;
            }
        }
        
        // 模拟填写姓氏
        const lastNameSelectors = ['input[name="lastName"]', 'input[name="last_name"]', '#lastName'];
        for (const selector of lastNameSelectors) {
            try {
                await browser.waitForElement(selector, { timeout: 3000 });
                await browser.fillInput(selector, studentData.lastName);
                console.log(`✅ 姓氏填写成功: ${studentData.lastName}`);
                break;
            } catch (error) {
                continue;
            }
        }
        
        // 截图记录
        await browser.takeScreenshot('test-form-filled');
        
        console.log('✅ 表单填写模拟完成');
        console.log('💡 注意: 这只是模拟测试，未实际提交表单');
        
        return true;
        
    } catch (error) {
        console.error('❌ 表单填写测试失败:', error.message);
        return false;
    }
}

/**
 * 测试注册流程（干运行模式）
 */
async function testRegistrationFlowDryRun(registrationFlow, studentData) {
    console.log('\n🧪 测试5: 注册流程干运行');
    console.log('================================');
    
    try {
        console.log('🔄 执行注册流程（干运行模式）...');
        console.log('💡 注意: 这是测试模式，不会实际提交注册');
        
        // 只测试导航到注册页面
        await registrationFlow.navigateToRegistrationPage();
        console.log('✅ 注册页面导航测试成功');
        
        console.log('\n📋 注册流程步骤预览:');
        console.log('1. ✅ 导航到注册页面');
        console.log('2. ⏳ 填写注册表单（跳过）');
        console.log('3. ⏳ 提交注册表单（跳过）');
        console.log('4. ⏳ 处理邮件验证（跳过）');
        console.log('5. ⏳ 完成账户激活（跳过）');
        
        console.log('\n💡 完整注册流程说明:');
        console.log('- 系统会自动填写学生信息');
        console.log('- 提交后会等待UNR发送验证邮件');
        console.log('- 自动从邮件中提取PIN码');
        console.log('- 使用PIN码完成账户验证');
        
        return true;
        
    } catch (error) {
        console.error('❌ 注册流程干运行失败:', error.message);
        return false;
    }
}

/**
 * 运行注册流程测试套件
 */
async function runRegistrationTests() {
    console.log('🚀 UNR注册流程测试套件');
    console.log('=====================================\n');
    
    let browser = null;
    
    try {
        // 1. 测试组件初始化
        const components = await testRegistrationComponents();
        if (!components) {
            console.log('❌ 组件初始化失败，无法继续测试');
            return false;
        }
        
        const { studentData, browser: browserInstance, registrationFlow } = components;
        browser = browserInstance;
        
        // 2. 测试注册页面访问
        const pageAccessResult = await testRegistrationPageAccess(browser);
        
        // 3. 测试表单元素识别
        const elementRecognitionResult = await testFormElementRecognition(browser);
        
        // 4. 测试表单填写
        const formFillingResult = await testFormFilling(browser, studentData);
        
        // 5. 测试注册流程干运行
        const dryRunResult = await testRegistrationFlowDryRun(registrationFlow, studentData);
        
        // 输出测试总结
        console.log('\n📊 注册流程测试结果总结:');
        console.log('=====================================');
        
        const results = [
            { name: '组件初始化', success: true },
            { name: '注册页面访问', success: pageAccessResult },
            { name: '表单元素识别', success: elementRecognitionResult },
            { name: '表单填写模拟', success: formFillingResult },
            { name: '注册流程干运行', success: dryRunResult }
        ];
        
        results.forEach(result => {
            const status = result.success ? '✅ 通过' : '❌ 失败';
            console.log(`${result.name}: ${status}`);
        });
        
        const passedTests = results.filter(r => r.success).length;
        const totalTests = results.length;
        
        console.log(`\n总计: ${passedTests}/${totalTests} 个测试通过`);
        
        if (passedTests === totalTests) {
            console.log('🎉 所有注册流程测试通过！');
            console.log('\n💡 下一步:');
            console.log('1. 确保Excel文件包含真实的学生数据');
            console.log('2. 配置学生邮箱的IMAP访问');
            console.log('3. 运行完整的注册流程');
        } else {
            console.log('⚠️ 部分测试失败，请检查网站结构或网络连接');
        }
        
        return passedTests === totalTests;
        
    } catch (error) {
        console.error('❌ 注册流程测试异常:', error.message);
        return false;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
    runRegistrationTests().catch(console.error);
}

export { runRegistrationTests };
