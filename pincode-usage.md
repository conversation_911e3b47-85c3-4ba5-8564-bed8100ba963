# PIN码获取功能使用指南

## 功能概述

PIN码获取功能是UNR自动注册系统的核心组件，负责：
1. 从Excel文件读取学生邮箱地址
2. 监听指定邮箱的UNR验证邮件
3. 自动提取9位PIN码
4. 管理多邮箱的PIN码获取流程

## 工作流程

```
Excel读取 → 邮箱配置 → 注册触发 → 邮件监听 → PIN码提取 → 验证完成
```

### 详细步骤

1. **数据准备**
   - 在Excel文件中配置学生邮箱地址
   - 确保邮箱IMAP服务已开启

2. **注册触发**
   - 系统在UNR网站自动注册账户
   - UNR发送验证邮件到学生邮箱

3. **邮件监听**
   - 系统自动连接学生邮箱
   - 实时监听新的验证邮件

4. **PIN码提取**
   - 识别来自UNR的验证邮件
   - 从邮件内容中提取9位PIN码

5. **验证完成**
   - 使用PIN码完成账户验证
   - 继续后续申请流程

## 使用方法

### 1. 基本测试

```bash
# 测试所有功能
npm test

# 单独测试PIN码功能
npm run test:pin

# 测试邮件连接
npm run test:email
```

### 2. 配置邮箱

在`.env`文件中配置：

```env
# 学生邮箱密码（如果需要测试连接）
STUDENT_EMAIL_PASSWORD=student_email_password

# 默认邮箱配置（用于发送方）
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=pnsbpvwlrvpybiei
```

### 3. Excel数据格式

确保Excel文件包含邮箱字段：

| Email | First Name | Last Name |
|-------|------------|-----------|
| <EMAIL> | John | Smith |

### 4. 编程接口

#### 基本用法

```javascript
import { PinCodeManager } from './utils/pinCodeManager.js';
import { ExcelReader } from './utils/excelReader.js';

// 1. 读取学生数据
const reader = new ExcelReader('test.xlsx');
const studentData = await reader.readStudentData();

// 2. 创建PIN码管理器
const pinManager = new PinCodeManager();

// 3. 获取PIN码
const pinCode = await pinManager.getPinCodeFromStudentData(studentData);
console.log('PIN码:', pinCode);
```

#### 高级用法

```javascript
// 批量处理多个邮箱
const emails = ['<EMAIL>', '<EMAIL>'];
const results = await pinManager.getPinCodesForEmails(emails, {
    maxWaitTime: 300000,  // 5分钟超时
    concurrent: 1,        // 串行处理
    batchDelay: 5000     // 批次间延迟5秒
});

// 测试邮箱连接
const isConnected = await pinManager.testEmailConnection(
    '<EMAIL>', 
    'email_password'
);

// 获取管理器状态
const status = pinManager.getStatus();
console.log('状态:', status);
```

## 支持的邮箱服务商

系统自动识别以下邮箱服务商的IMAP配置：

| 服务商 | 域名 | IMAP服务器 | 端口 |
|--------|------|------------|------|
| QQ邮箱 | qq.com | imap.qq.com | 993 |
| Gmail | gmail.com | imap.gmail.com | 993 |
| 163邮箱 | 163.com | imap.163.com | 993 |
| 126邮箱 | 126.com | imap.126.com | 993 |
| Outlook | outlook.com | outlook.office365.com | 993 |
| Hotmail | hotmail.com | outlook.office365.com | 993 |

## PIN码识别规则

系统支持多种PIN码格式：

```
✅ temporary PIN: 886758143
✅ PIN: 886758143
✅ enter the following temporary PIN: 886758143
✅ When requested for a PIN, enter: 886758143
✅ 886758143 (纯数字)
```

### 邮件识别条件

- **发件人**: 包含 `unr.edu` 域名
- **主题**: 包含 `Nevada` 关键词
- **内容**: 包含 `temporary PIN` 关键词
- **格式**: 9位纯数字PIN码

## 故障排除

### 常见问题

1. **Excel读取失败**
   ```
   ❌ Excel文件不存在: ./test.xlsx
   ```
   **解决方案**: 确保test.xlsx文件在项目根目录

2. **邮箱连接失败**
   ```
   ❌ IMAP连接失败: Invalid credentials
   ```
   **解决方案**: 
   - 检查邮箱地址和密码
   - 确认IMAP服务已开启
   - 使用应用专用密码

3. **PIN码提取失败**
   ```
   ⚠️ 未找到有效的9位PIN码
   ```
   **解决方案**:
   - 确认邮件来自UNR
   - 检查邮件内容格式
   - 查看邮件是否在垃圾箱

4. **等待超时**
   ```
   ❌ 等待验证邮件超时
   ```
   **解决方案**:
   - 检查网络连接
   - 确认注册流程已触发
   - 增加等待时间

### 调试模式

启用详细日志：

```javascript
const pinManager = new PinCodeManager({
    debug: true,
    verbose: true
});
```

查看邮件内容：

```javascript
const verificationInfo = await pinManager.getVerificationInfo();
console.log('邮件内容:', verificationInfo.emailContent);
```

## 性能优化

### 批量处理建议

1. **串行处理**: 避免并发过多导致服务器限制
2. **批次延迟**: 在批次间添加适当延迟
3. **超时控制**: 设置合理的等待时间
4. **错误重试**: 实现自动重试机制

### 资源管理

```javascript
// 及时清理资源
await pinManager.clearAllPinCodes();

// 关闭连接
await emailHandler.disconnect();
```

## 安全注意事项

1. **密码保护**
   - 使用环境变量存储密码
   - 不在代码中硬编码敏感信息

2. **权限控制**
   - 使用最小权限原则
   - 定期更换邮箱密码

3. **数据保护**
   - 及时清理PIN码缓存
   - 避免在日志中输出敏感信息

## API参考

### PinCodeManager

#### 构造函数
```javascript
new PinCodeManager(config)
```

#### 主要方法
- `getPinCodeFromStudentData(studentData, options)` - 从学生数据获取PIN码
- `getPinCodeForEmail(email, password, options)` - 为指定邮箱获取PIN码
- `getPinCodesForEmails(emails, password, options)` - 批量获取PIN码
- `testEmailConnection(email, password)` - 测试邮箱连接
- `getStoredPinCode(email)` - 获取已存储的PIN码
- `clearAllPinCodes()` - 清除所有PIN码缓存

#### 配置选项
- `maxWaitTime` - 最大等待时间（默认5分钟）
- `concurrent` - 并发数量（默认1）
- `batchDelay` - 批次延迟（默认5秒）

## 示例代码

完整的使用示例请参考：
- `src/test-pincode.js` - 功能测试示例
- `src/main.js` - 集成使用示例
- `src/test-email.js` - 邮件功能测试
