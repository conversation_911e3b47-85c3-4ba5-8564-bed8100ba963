/**
 * 邮件IMAP处理模块
 *
 * 功能：
 * 1. 连接指定邮箱IMAP服务器
 * 2. 搜索和读取UNR验证邮件
 * 3. 提取PIN码和激活链接
 * 4. 支持多邮箱账户管理
 *
 * <AUTHOR> Registration Bot
 */

import Imap from 'node-imap';
import { simpleParser } from 'mailparser';
import { EventEmitter } from 'events';

/**
 * 邮件处理器类
 */
export class EmailHandler extends EventEmitter {
    constructor(config) {
        super();

        // 支持动态邮箱配置
        this.emailAddress = config.user || config.email;
        this.emailPassword = config.password;
        this.emailServer = this.getEmailServer(this.emailAddress);

        this.config = {
            imap: {
                user: this.emailAddress,
                password: this.emailPassword,
                host: this.emailServer.host,
                port: this.emailServer.port,
                tls: true,
                tlsOptions: {
                    rejectUnauthorized: false
                }
            }
        };

        this.imap = null;
        this.isConnected = false;
        this.searchTimeout = 60000; // 60秒超时
    }

    /**
     * 根据邮箱地址自动识别IMAP服务器配置
     */
    getEmailServer(email) {
        const domain = email.split('@')[1].toLowerCase();

        // 支持多种邮箱服务商
        const serverConfigs = {
            'qq.com': { host: 'imap.qq.com', port: 993 },
            'gmail.com': { host: 'imap.gmail.com', port: 993 },
            '163.com': { host: 'imap.163.com', port: 993 },
            '126.com': { host: 'imap.126.com', port: 993 },
            'outlook.com': { host: 'outlook.office365.com', port: 993 },
            'hotmail.com': { host: 'outlook.office365.com', port: 993 },
            'mcpserver.sbs': { host: 'imap.mcpserver.sbs', port: 993 }, // 自定义域名
        };

        return serverConfigs[domain] || { host: `imap.${domain}`, port: 993 };
    }

    /**
     * 连接到IMAP服务器
     */
    async connect() {
        return new Promise((resolve, reject) => {
            try {
                console.log('📧 正在连接QQ邮箱IMAP服务器...');
                
                this.imap = new Imap(this.config.imap);
                
                // 连接成功事件
                this.imap.once('ready', () => {
                    console.log('✅ IMAP连接成功');
                    this.isConnected = true;
                    resolve();
                });
                
                // 连接错误事件
                this.imap.once('error', (err) => {
                    console.error('❌ IMAP连接失败:', err.message);
                    this.isConnected = false;
                    reject(err);
                });
                
                // 连接结束事件
                this.imap.once('end', () => {
                    console.log('📧 IMAP连接已断开');
                    this.isConnected = false;
                });
                
                // 开始连接
                this.imap.connect();
                
            } catch (error) {
                console.error('❌ IMAP连接异常:', error.message);
                reject(error);
            }
        });
    }

    /**
     * 断开IMAP连接
     */
    async disconnect() {
        if (this.imap && this.isConnected) {
            this.imap.end();
            this.isConnected = false;
            console.log('📧 IMAP连接已关闭');
        }
    }

    /**
     * 打开收件箱
     */
    async openInbox() {
        return new Promise((resolve, reject) => {
            this.imap.openBox('INBOX', false, (err, box) => {
                if (err) {
                    console.error('❌ 打开收件箱失败:', err.message);
                    reject(err);
                } else {
                    console.log('📬 收件箱已打开，邮件总数:', box.messages.total);
                    resolve(box);
                }
            });
        });
    }

    /**
     * 搜索UNR验证邮件
     */
    async searchVerificationEmails(searchCriteria = {}) {
        return new Promise((resolve, reject) => {
            // 搜索条件：来自UNR的邮件，包含PIN码
            const criteria = [
                ['FROM', 'unr.edu'], // 来自UNR
                ['SUBJECT', 'Nevada'], // 主题包含Nevada
                ['BODY', 'temporary PIN'], // 邮件内容包含temporary PIN
                ...Object.entries(searchCriteria).map(([key, value]) => [key, value])
            ];

            console.log('🔍 正在搜索UNR验证邮件...');
            console.log(`📧 搜索邮箱: ${this.emailAddress}`);

            this.imap.search(criteria, (err, results) => {
                if (err) {
                    console.error('❌ 邮件搜索失败:', err.message);
                    reject(err);
                } else {
                    console.log(`📧 找到 ${results.length} 封匹配的邮件`);
                    resolve(results);
                }
            });
        });
    }

    /**
     * 获取邮件内容
     */
    async fetchEmailContent(uid) {
        return new Promise((resolve, reject) => {
            const fetch = this.imap.fetch(uid, {
                bodies: '',
                markSeen: false
            });
            
            let emailData = '';
            
            fetch.on('message', (msg, seqno) => {
                msg.on('body', (stream, info) => {
                    stream.on('data', (chunk) => {
                        emailData += chunk.toString('utf8');
                    });
                    
                    stream.once('end', () => {
                        // 解析邮件内容
                        simpleParser(emailData)
                            .then(parsed => {
                                resolve(parsed);
                            })
                            .catch(reject);
                    });
                });
            });
            
            fetch.once('error', reject);
            fetch.once('end', () => {
                if (!emailData) {
                    reject(new Error('邮件内容为空'));
                }
            });
        });
    }

    /**
     * 从邮件内容中提取PIN码
     */
    extractPinCode(emailContent) {
        try {
            const text = emailContent.text || emailContent.html || '';

            // 专门针对UNR邮件的PIN码匹配模式
            const pinPatterns = [
                /temporary\s+PIN:\s*(\d{9})/i,       // temporary PIN: 886758143
                /PIN:\s*(\d{9})/i,                   // PIN: 886758143
                /enter.*?PIN:\s*(\d{9})/i,           // enter the following temporary PIN: 886758143
                /requested.*?PIN.*?(\d{9})/i,        // When requested for a PIN, enter: 886758143
                /(\d{9})/g                           // 任何9位数字（最后备选）
            ];

            console.log('🔍 正在提取PIN码...');

            for (const pattern of pinPatterns) {
                const match = text.match(pattern);
                if (match) {
                    const pin = match[1] || match[0];
                    if (pin && pin.length === 9 && /^\d{9}$/.test(pin)) {
                        console.log(`🔑 成功提取PIN码: ${pin}`);
                        return pin;
                    }
                }
            }

            console.warn('⚠️ 未找到有效的9位PIN码');
            console.log('邮件内容预览:', text.substring(0, 200) + '...');
            return null;

        } catch (error) {
            console.error('❌ PIN码提取失败:', error.message);
            return null;
        }
    }

    /**
     * 从邮件内容中提取激活链接
     */
    extractActivationLink(emailContent) {
        try {
            const text = emailContent.text || emailContent.html || '';
            
            // 激活链接匹配模式
            const linkPatterns = [
                /https?:\/\/[^\s]+activate[^\s]*/i,
                /https?:\/\/[^\s]+auth[^\s]*/i,
                /https?:\/\/admissions\.unr\.edu[^\s]*/i
            ];
            
            for (const pattern of linkPatterns) {
                const match = text.match(pattern);
                if (match) {
                    const link = match[0];
                    console.log('🔗 找到激活链接:', link);
                    return link;
                }
            }
            
            console.warn('⚠️ 未找到激活链接');
            return null;
            
        } catch (error) {
            console.error('❌ 激活链接提取失败:', error.message);
            return null;
        }
    }

    /**
     * 等待并获取验证邮件
     */
    async waitForVerificationEmail(maxWaitTime = 300000) { // 5分钟超时
        console.log('⏳ 等待验证邮件...');
        
        const startTime = Date.now();
        const checkInterval = 10000; // 每10秒检查一次
        
        while (Date.now() - startTime < maxWaitTime) {
            try {
                // 搜索验证邮件
                const emailIds = await this.searchVerificationEmails();
                
                if (emailIds.length > 0) {
                    console.log('📧 找到验证邮件，正在处理...');
                    
                    // 获取最新的邮件
                    const latestEmailId = emailIds[emailIds.length - 1];
                    const emailContent = await this.fetchEmailContent(latestEmailId);
                    
                    // 提取PIN码和激活链接
                    const pinCode = this.extractPinCode(emailContent);
                    const activationLink = this.extractActivationLink(emailContent);
                    
                    if (pinCode) {
                        console.log('✅ 验证邮件处理完成');
                        return {
                            pinCode,
                            activationLink,
                            emailContent,
                            subject: emailContent.subject,
                            from: emailContent.from?.text,
                            date: emailContent.date
                        };
                    }
                }
                
                // 等待下次检查
                console.log('⏳ 未找到验证邮件，等待中...');
                await this.sleep(checkInterval);
                
            } catch (error) {
                console.error('❌ 检查邮件时发生错误:', error.message);
                await this.sleep(checkInterval);
            }
        }
        
        throw new Error('等待验证邮件超时');
    }

    /**
     * 获取验证信息（主要方法）
     */
    async getVerificationInfo(options = {}) {
        try {
            // 连接到邮箱
            await this.connect();
            
            // 打开收件箱
            await this.openInbox();
            
            // 等待验证邮件
            const verificationInfo = await this.waitForVerificationEmail(
                options.maxWaitTime || 300000
            );
            
            return verificationInfo;
            
        } catch (error) {
            console.error('❌ 获取验证信息失败:', error.message);
            throw error;
        } finally {
            // 断开连接
            await this.disconnect();
        }
    }

    /**
     * 快速获取PIN码（简化方法）
     */
    async getPinCode(options = {}) {
        try {
            const verificationInfo = await this.getVerificationInfo(options);
            return verificationInfo.pinCode;
        } catch (error) {
            console.error('❌ 获取PIN码失败:', error.message);
            throw error;
        }
    }

    /**
     * 工具方法：延时
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 测试邮箱连接
     */
    async testConnection() {
        try {
            console.log('🧪 测试邮箱连接...');
            await this.connect();
            await this.openInbox();
            console.log('✅ 邮箱连接测试成功');
            return true;
        } catch (error) {
            console.error('❌ 邮箱连接测试失败:', error.message);
            return false;
        } finally {
            await this.disconnect();
        }
    }
}
