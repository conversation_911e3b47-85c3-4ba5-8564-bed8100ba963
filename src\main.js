/**
 * UNR自动注册和申请系统 - 主程序入口
 * 
 * 功能：
 * 1. 读取Excel学生数据
 * 2. 自动注册UNR账户
 * 3. 自动填写申请表单
 * 4. 邮件验证集成
 * 
 * <AUTHOR> Registration Bot
 * @version 1.0.0
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs-extra';
import dotenv from 'dotenv';

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

// 加载环境变量
dotenv.config();

// 导入模块
import { ExcelReader } from './utils/excelReader.js';
import { EmailHandler } from './utils/emailHandler.js';
import { PinCodeManager } from './utils/pinCodeManager.js';
import { BrowserController } from './automation/browserController.js';
import { RegistrationFlow } from './automation/registrationFlow.js';
import { ApplicationFlow } from './automation/applicationFlow.js';
import { UNRController } from './unr/unrController.js';
import { Logger } from './utils/logger.js';

/**
 * 主程序类
 */
class UNRAutoRegistration {
    constructor() {
        this.projectRoot = projectRoot;
        this.excelReader = null;
        this.emailHandler = null;
        this.pinCodeManager = null;
        this.browserController = null;
        this.registrationFlow = null;
        this.applicationFlow = null;
        this.unrController = null;
        this.logger = null;
        this.studentData = null;
        this.config = {
            // Excel文件路径
            excelPath: join(projectRoot, 'test.xlsx'),
            
            // UNR网站URLs
            urls: {
                register: 'https://admissions.unr.edu/account/register',
                login: 'https://admissions.unr.edu/account/login',
                apply: 'https://admissions.unr.edu/apply/'
            },
            
            // 邮件配置
            email: {
                server: 'imap.qq.com',
                port: 993,
                user: '<EMAIL>',
                password: 'pnsbpvwlrvpybiei',
                folder: 'INBOX',
                protocol: 'IMAP'
            },
            
            // 浏览器配置
            browser: {
                headless: false, // 设为true可隐藏浏览器窗口
                defaultViewport: {
                    width: 1280,
                    height: 720
                },
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            }
        };
    }

    /**
     * 初始化检查
     */
    async initialize() {
        console.log('🚀 UNR自动注册系统启动中...');
        
        // 检查Excel文件是否存在
        if (!await fs.pathExists(this.config.excelPath)) {
            throw new Error(`Excel文件不存在: ${this.config.excelPath}`);
        }
        
        // 创建日志目录
        const logsDir = join(this.projectRoot, 'logs');
        await fs.ensureDir(logsDir);
        
        // 创建截图目录
        const screenshotsDir = join(this.projectRoot, 'screenshots');
        await fs.ensureDir(screenshotsDir);

        // 初始化Excel读取器
        this.excelReader = new ExcelReader(this.config.excelPath);

        // 初始化邮件处理器
        this.emailHandler = new EmailHandler(this.config.email);

        // 初始化PIN码管理器
        this.pinCodeManager = new PinCodeManager({
            defaultPassword: this.config.email.password
        });

        // 初始化浏览器控制器
        this.browserController = new BrowserController(this.config.browser);

        // 初始化日志系统
        this.logger = new Logger({
            level: this.config.debug ? 'debug' : 'info',
            enableConsole: true,
            enableFile: true
        });

        // 等待日志系统初始化完成
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 初始化注册流程控制器（传递日志器）
        this.registrationFlow = new RegistrationFlow(this.browserController, {
            ...this.config,
            logger: this.logger
        });

        // 初始化申请流程控制器（传递日志器）
        this.applicationFlow = new ApplicationFlow(this.browserController, {
            ...this.config,
            logger: this.logger
        });

        // 初始化UNR专用控制器
        this.unrController = new UNRController(this.browserController, {
            ...this.config,
            logger: this.logger,
            enablePageAnalysis: true,
            enableAdaptiveStrategy: true
        });

        console.log('✅ 初始化完成');
    }

    /**
     * 运行主流程
     */
    async run() {
        try {
            await this.initialize();

            // 记录流程开始
            this.logger.logFlowStart('UNR Auto Registration', {
                studentEmail: this.studentData?.email || 'unknown'
            });

            console.log('📊 正在读取Excel数据...');
            this.logger.logDataProcessing('Excel data reading started');
            this.studentData = await this.excelReader.readStudentData();
            console.log('✅ Excel数据读取完成');
            this.logger.logDataProcessing('Excel data reading completed', {
                studentName: `${this.studentData.firstName} ${this.studentData.lastName}`,
                email: this.studentData.email
            });

            console.log('🌐 启动浏览器...');
            this.logger.logBrowser('info', 'Browser launch started');
            await this.browserController.launch();
            this.logger.logBrowser('info', 'Browser launch completed');

            console.log('📝 开始注册流程...');
            this.logger.logRegistration('info', 'Registration flow started', {
                email: this.studentData.email
            });
            const registrationResult = await this.registrationFlow.execute(this.studentData);
            console.log('✅ 注册流程完成:', registrationResult);
            this.logger.logRegistration('info', 'Registration flow completed', registrationResult);

            // 检查注册是否成功并且已经自动登录
            if (registrationResult.success) {
                console.log('\n🔄 注册成功，准备开始申请流程...');
                this.logger.logApplication('info', 'Application flow preparation started');

                // 如果注册过程中已经自动登录，直接开始申请
                console.log('📋 开始申请流程...');
                this.logger.logApplication('info', 'Application flow started', {
                    email: this.studentData.email
                });
                const applicationResult = await this.applicationFlow.execute(this.studentData);
                console.log('✅ 申请流程完成:', applicationResult);
                this.logger.logApplication('info', 'Application flow completed', applicationResult);

                // 输出完整流程结果
                console.log('\n🎉 完整流程执行结果:');
                console.log('=====================================');
                console.log(`👤 学生: ${this.studentData.firstName} ${this.studentData.lastName}`);
                console.log(`📧 邮箱: ${this.studentData.email}`);
                console.log(`✅ 注册状态: ${registrationResult.success ? '成功' : '失败'}`);
                console.log(`✅ 申请状态: ${applicationResult.success ? '成功' : '失败'}`);

                if (applicationResult.success) {
                    console.log(`📊 完成步骤: ${applicationResult.completedSteps.length}/${applicationResult.totalSteps}`);
                }

                // 记录完整流程结果
                this.logger.logFlowEnd('UNR Auto Registration', {
                    registrationSuccess: registrationResult.success,
                    applicationSuccess: applicationResult.success,
                    completedSteps: applicationResult.completedSteps?.length || 0,
                    totalSteps: applicationResult.totalSteps || 0
                });
            } else {
                console.log('❌ 注册失败，无法继续申请流程');
                this.logger.logRegistration('error', 'Registration failed, stopping flow', registrationResult);
                this.logger.logFlowEnd('UNR Auto Registration', {
                    registrationSuccess: false,
                    applicationSuccess: false,
                    error: 'Registration failed'
                });
            }
            
            console.log('🎉 所有流程执行完成！');

        } catch (error) {
            console.error('❌ 执行过程中发生错误:', error.message);
            console.error(error.stack);

            // 记录错误
            this.logger.logError(error, {
                operation: 'main_flow',
                studentEmail: this.studentData?.email
            });

        } finally {
            // 确保浏览器被关闭
            if (this.browserController && this.browserController.isReady()) {
                this.logger.logBrowser('info', 'Browser cleanup started');
                await this.browserController.close();
                this.logger.logBrowser('info', 'Browser cleanup completed');
            }

            // 关闭日志系统
            if (this.logger) {
                await this.logger.close();
            }
        }
    }

    /**
     * 演示PIN码获取流程
     */
    async demonstratePinCodeFlow() {
        try {
            await this.initialize();

            console.log('📊 正在读取Excel数据...');
            this.studentData = await this.excelReader.readStudentData();

            console.log('\n🔑 开始PIN码获取演示...');
            console.log('=====================================');

            // 从学生数据中获取邮箱
            const studentEmail = this.studentData.email;
            console.log(`📧 学生邮箱: ${studentEmail}`);

            // 测试邮箱连接
            console.log('\n1️⃣ 测试邮箱连接...');
            const connectionTest = await this.pinCodeManager.testEmailConnection(studentEmail);

            if (!connectionTest) {
                console.log('❌ 邮箱连接失败，请检查配置');
                return;
            }

            // 获取PIN码（这里只是演示，实际需要先触发注册）
            console.log('\n2️⃣ 等待验证邮件...');
            console.log('💡 提示：在实际使用中，这一步会在注册后自动执行');
            console.log('💡 现在可以手动发送测试邮件到该邮箱来测试PIN码提取功能');

            try {
                // 设置较短的超时时间用于演示
                const pinCode = await this.pinCodeManager.getPinCodeFromStudentData(this.studentData, {
                    maxWaitTime: 30000 // 30秒超时
                });

                console.log(`✅ 成功获取PIN码: ${pinCode}`);

            } catch (error) {
                console.log('⏳ 未收到验证邮件（这是正常的，因为还没有触发注册）');
                console.log('💡 在实际流程中，系统会在注册后自动等待验证邮件');
            }

            // 显示PIN码管理器状态
            console.log('\n3️⃣ PIN码管理器状态:');
            const status = this.pinCodeManager.getStatus();
            console.log('📊 状态信息:', status);

            console.log('\n✅ PIN码获取流程演示完成');

        } catch (error) {
            console.error('❌ PIN码流程演示失败:', error.message);
            throw error;
        }
    }
}

// 程序入口
async function main() {
    const app = new UNRAutoRegistration();
    await app.run();
}

// 如果直接运行此文件，则执行主程序
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}

export { UNRAutoRegistration };
