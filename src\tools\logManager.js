#!/usr/bin/env node

/**
 * 日志管理命令行工具
 * 
 * 功能：
 * 1. 查看日志统计
 * 2. 分析日志文件
 * 3. 清理旧日志
 * 4. 导出日志数据
 * 
 * <AUTHOR> Registration Bot
 */

import { LogAnalyzer } from '../utils/logAnalyzer.js';
import { Logger } from '../utils/logger.js';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// 获取项目根目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(dirname(__dirname));

/**
 * 日志管理器类
 */
class LogManager {
    constructor() {
        this.logDir = path.join(projectRoot, 'logs');
        this.reportsDir = path.join(projectRoot, 'reports');
        this.analyzer = new LogAnalyzer({
            logDir: this.logDir,
            outputDir: this.reportsDir
        });
    }

    /**
     * 显示帮助信息
     */
    showHelp() {
        console.log(`
🚀 UNR自动化日志管理工具

用法: node src/tools/logManager.js <命令> [选项]

命令:
  status          显示日志状态
  analyze         分析所有日志文件
  quick           快速分析报告
  clean           清理旧日志文件
  export          导出日志数据
  list            列出所有日志文件
  tail            实时查看最新日志
  help            显示此帮助信息

选项:
  --days <数字>   清理多少天前的日志 (默认: 7)
  --format <格式> 导出格式 json|csv (默认: json)
  --output <路径> 输出文件路径
  --lines <数字>  tail命令显示的行数 (默认: 50)

示例:
  node src/tools/logManager.js status
  node src/tools/logManager.js analyze
  node src/tools/logManager.js clean --days 3
  node src/tools/logManager.js export --format csv --output logs.csv
  node src/tools/logManager.js tail --lines 100
        `);
    }

    /**
     * 显示日志状态
     */
    async showStatus() {
        try {
            console.log('📊 日志状态检查');
            console.log('=====================================');
            
            // 检查日志目录
            const logDirExists = await fs.pathExists(this.logDir);
            console.log(`📁 日志目录: ${this.logDir}`);
            console.log(`📁 目录状态: ${logDirExists ? '✅ 存在' : '❌ 不存在'}`);
            
            if (!logDirExists) {
                console.log('💡 提示: 运行程序后会自动创建日志目录');
                return;
            }
            
            // 列出日志文件
            const files = await fs.readdir(this.logDir);
            const logFiles = files.filter(file => file.endsWith('.log'));
            
            console.log(`📋 日志文件数量: ${logFiles.length}`);
            
            if (logFiles.length === 0) {
                console.log('💡 提示: 未找到日志文件，运行程序后会自动生成');
                return;
            }
            
            console.log('\n📄 日志文件列表:');
            for (const file of logFiles) {
                const filePath = path.join(this.logDir, file);
                const stats = await fs.stat(filePath);
                const sizeKB = Math.round(stats.size / 1024);
                const modified = stats.mtime.toLocaleString();
                
                console.log(`  📄 ${file}`);
                console.log(`     大小: ${sizeKB} KB`);
                console.log(`     修改: ${modified}`);
            }
            
            // 快速统计
            await this.analyzer.generateQuickReport();
            
        } catch (error) {
            console.error('❌ 获取日志状态失败:', error.message);
        }
    }

    /**
     * 分析日志文件
     */
    async analyzeLogs() {
        try {
            console.log('🔍 开始完整日志分析...');
            
            const analysis = await this.analyzer.analyzeAllLogs();
            
            if (analysis) {
                console.log('\n📊 分析完成，报告已保存到 reports/ 目录');
                console.log('💡 可以打开HTML文件查看详细报告');
            }
            
        } catch (error) {
            console.error('❌ 日志分析失败:', error.message);
        }
    }

    /**
     * 快速分析
     */
    async quickAnalyze() {
        try {
            await this.analyzer.generateQuickReport();
        } catch (error) {
            console.error('❌ 快速分析失败:', error.message);
        }
    }

    /**
     * 清理旧日志
     */
    async cleanOldLogs(days = 7) {
        try {
            console.log(`🧹 清理 ${days} 天前的日志文件...`);
            
            const logDirExists = await fs.pathExists(this.logDir);
            if (!logDirExists) {
                console.log('📁 日志目录不存在');
                return;
            }
            
            const files = await fs.readdir(this.logDir);
            const logFiles = files.filter(file => file.endsWith('.log'));
            const maxAge = days * 24 * 60 * 60 * 1000; // 转换为毫秒
            const now = Date.now();
            
            let cleanedCount = 0;
            
            for (const file of logFiles) {
                const filePath = path.join(this.logDir, file);
                const stats = await fs.stat(filePath);
                
                if (now - stats.mtime.getTime() > maxAge) {
                    await fs.remove(filePath);
                    console.log(`🗑️ 已删除: ${file}`);
                    cleanedCount++;
                }
            }
            
            if (cleanedCount === 0) {
                console.log('✅ 没有需要清理的旧日志文件');
            } else {
                console.log(`✅ 已清理 ${cleanedCount} 个旧日志文件`);
            }
            
        } catch (error) {
            console.error('❌ 清理日志失败:', error.message);
        }
    }

    /**
     * 导出日志数据
     */
    async exportLogs(format = 'json', outputPath = null) {
        try {
            console.log(`📤 导出日志数据 (格式: ${format})...`);
            
            // 创建临时Logger实例用于导出
            const logger = new Logger({
                logDir: this.logDir,
                enableConsole: false,
                enableFile: false
            });
            
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const defaultOutput = outputPath || path.join(this.reportsDir, `logs-export-${timestamp}.${format}`);
            
            // 确保输出目录存在
            await fs.ensureDir(path.dirname(defaultOutput));
            
            const count = await logger.exportLogs(defaultOutput, format);
            
            if (count > 0) {
                console.log(`✅ 已导出 ${count} 条日志记录到: ${defaultOutput}`);
            } else {
                console.log('⚠️ 没有找到可导出的日志数据');
            }
            
        } catch (error) {
            console.error('❌ 导出日志失败:', error.message);
        }
    }

    /**
     * 列出日志文件
     */
    async listLogFiles() {
        try {
            console.log('📋 日志文件列表');
            console.log('=====================================');
            
            const logDirExists = await fs.pathExists(this.logDir);
            if (!logDirExists) {
                console.log('📁 日志目录不存在');
                return;
            }
            
            const files = await fs.readdir(this.logDir);
            const logFiles = files.filter(file => file.endsWith('.log'));
            
            if (logFiles.length === 0) {
                console.log('📄 未找到日志文件');
                return;
            }
            
            for (const file of logFiles) {
                const filePath = path.join(this.logDir, file);
                const stats = await fs.stat(filePath);
                const sizeKB = Math.round(stats.size / 1024);
                const modified = stats.mtime.toLocaleString();
                
                console.log(`📄 ${file}`);
                console.log(`   大小: ${sizeKB} KB | 修改: ${modified}`);
                console.log(`   路径: ${filePath}`);
                console.log('');
            }
            
        } catch (error) {
            console.error('❌ 列出日志文件失败:', error.message);
        }
    }

    /**
     * 实时查看日志
     */
    async tailLogs(lines = 50) {
        try {
            console.log(`📖 显示最新 ${lines} 行日志`);
            console.log('=====================================');
            
            const mainLogPath = path.join(this.logDir, 'main.log');
            const logExists = await fs.pathExists(mainLogPath);
            
            if (!logExists) {
                console.log('📄 主日志文件不存在');
                return;
            }
            
            const content = await fs.readFile(mainLogPath, 'utf8');
            const logLines = content.split('\n').filter(line => line.trim());
            const recentLines = logLines.slice(-lines);
            
            for (const line of recentLines) {
                try {
                    const logEntry = JSON.parse(line);
                    const timestamp = new Date(logEntry.timestamp).toLocaleString();
                    const level = logEntry.level.toUpperCase().padEnd(5);
                    const category = logEntry.category.toUpperCase().padEnd(12);
                    
                    console.log(`${timestamp} [${category}] ${level}: ${logEntry.message}`);
                } catch (e) {
                    // 如果不是JSON格式，直接输出
                    console.log(line);
                }
            }
            
            console.log(`\n📊 显示了最新 ${recentLines.length} 行日志`);
            
        } catch (error) {
            console.error('❌ 查看日志失败:', error.message);
        }
    }

    /**
     * 解析命令行参数
     */
    parseArgs(args) {
        const options = {};
        const commands = [];
        
        for (let i = 0; i < args.length; i++) {
            const arg = args[i];
            
            if (arg.startsWith('--')) {
                const key = arg.substring(2);
                const value = args[i + 1];
                options[key] = value;
                i++; // 跳过下一个参数
            } else {
                commands.push(arg);
            }
        }
        
        return { commands, options };
    }

    /**
     * 运行命令
     */
    async run(args) {
        const { commands, options } = this.parseArgs(args);
        const command = commands[0];
        
        switch (command) {
            case 'status':
                await this.showStatus();
                break;
                
            case 'analyze':
                await this.analyzeLogs();
                break;
                
            case 'quick':
                await this.quickAnalyze();
                break;
                
            case 'clean':
                const days = parseInt(options.days) || 7;
                await this.cleanOldLogs(days);
                break;
                
            case 'export':
                const format = options.format || 'json';
                const output = options.output;
                await this.exportLogs(format, output);
                break;
                
            case 'list':
                await this.listLogFiles();
                break;
                
            case 'tail':
                const lines = parseInt(options.lines) || 50;
                await this.tailLogs(lines);
                break;
                
            case 'help':
            default:
                this.showHelp();
                break;
        }
    }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
    const logManager = new LogManager();
    const args = process.argv.slice(2);
    
    logManager.run(args).catch(error => {
        console.error('❌ 执行失败:', error.message);
        process.exit(1);
    });
}

export { LogManager };
