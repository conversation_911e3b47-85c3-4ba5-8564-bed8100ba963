# UNR账户注册自动化指南

## 功能概述

注册流程自动化模块实现了完整的UNR账户注册流程：
1. 自动导航到注册页面
2. 智能填写注册表单
3. 处理邮件验证和PIN码
4. 完成账户激活

## 注册流程步骤

### 完整流程图

```
开始 → 导航注册页面 → 填写表单 → 提交表单 → 邮件验证 → PIN码输入 → 账户激活 → 完成
```

### 详细步骤说明

#### 步骤1: 导航到注册页面
- 访问 `https://admissions.unr.edu/account/register`
- 等待页面完全加载
- 验证页面标题和内容

#### 步骤2: 填写注册表单
- **邮箱地址**: 从Excel数据中读取
- **姓名信息**: 填写First Name和Last Name
- **生日信息**: 如果需要，格式化后填写
- **密码设置**: 自动生成安全密码
- **条款同意**: 自动勾选使用条款

#### 步骤3: 提交注册表单
- 查找并点击提交按钮
- 等待页面响应
- 检查提交结果

#### 步骤4: 邮件验证处理
- 监听学生邮箱
- 识别UNR验证邮件
- 提取9位PIN码

#### 步骤5: 完成账户激活
- 输入PIN码
- 提交验证
- 确认激活成功

## 使用方法

### 基本用法

```javascript
import { RegistrationFlow } from './automation/registrationFlow.js';
import { BrowserController } from './automation/browserController.js';

// 创建浏览器控制器
const browser = new BrowserController({
    headless: false,
    timeout: 30000
});

await browser.launch();

// 创建注册流程控制器
const registrationFlow = new RegistrationFlow(browser);

// 执行注册流程
const result = await registrationFlow.execute(studentData);
console.log('注册结果:', result);

await browser.close();
```

### 集成使用

```javascript
import { UNRAutoRegistration } from './main.js';

// 使用主程序执行完整流程
const app = new UNRAutoRegistration();
await app.run();
```

## 配置选项

### 注册流程配置

```javascript
const config = {
    registerUrl: 'https://admissions.unr.edu/account/register',
    loginUrl: 'https://admissions.unr.edu/account/login',
    maxRetries: 3,              // 最大重试次数
    retryDelay: 5000,           // 重试延迟（毫秒）
    pinWaitTime: 300000         // PIN码等待时间（5分钟）
};
```

### 学生数据格式

```javascript
const studentData = {
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Smith',
    birthDate: '1995-05-15',    // 可选
    // 其他字段...
};
```

## 智能表单识别

### 支持的字段选择器

#### 邮箱字段
```javascript
const emailSelectors = [
    'input[name="email"]',
    'input[type="email"]',
    '#email',
    '#Email',
    'input[placeholder*="email" i]'
];
```

#### 姓名字段
```javascript
const firstNameSelectors = [
    'input[name="firstName"]',
    'input[name="first_name"]',
    'input[name="FirstName"]',
    '#firstName',
    '#first-name'
];
```

#### 密码字段
```javascript
const passwordSelectors = [
    'input[name="password"]',
    'input[type="password"]',
    '#password',
    '#Password'
];
```

### 自适应策略

系统使用多选择器策略，按优先级尝试：
1. **name属性选择器** - 最稳定
2. **type属性选择器** - 通用性好
3. **ID选择器** - 精确匹配
4. **placeholder选择器** - 备用方案

## 错误处理

### 常见错误类型

1. **页面导航失败**
   ```
   ❌ 导航失败: net::ERR_INTERNET_DISCONNECTED
   ```
   **解决方案**: 检查网络连接

2. **表单元素未找到**
   ```
   ❌ 等待元素失败: input[name="email"]
   ```
   **解决方案**: 网站结构可能已变化，需要更新选择器

3. **邮件验证超时**
   ```
   ❌ 等待验证邮件超时
   ```
   **解决方案**: 检查邮箱配置和网络连接

### 错误恢复机制

- **自动截图**: 错误发生时自动保存截图
- **重试机制**: 支持配置重试次数和延迟
- **状态记录**: 记录当前执行步骤
- **详细日志**: 提供调试信息

## 测试验证

### 运行测试

```bash
# 测试注册流程
npm run test:registration

# 测试所有功能
npm test
```

### 测试项目

1. **组件初始化** - 验证所有组件正常创建
2. **注册页面访问** - 测试UNR网站连接
3. **表单元素识别** - 检查表单字段是否可识别
4. **表单填写模拟** - 验证填写功能
5. **注册流程干运行** - 测试完整流程（不提交）

### 调试模式

启用详细调试：

```javascript
const browser = new BrowserController({
    headless: false,    // 显示浏览器窗口
    timeout: 60000      // 增加超时时间
});
```

查看截图：
- 所有关键步骤都会自动截图
- 错误发生时会保存错误截图
- 截图保存在 `screenshots/` 目录

## 安全注意事项

### 密码生成

系统自动生成安全密码：
```javascript
generatePassword(studentData) {
    const base = studentData.lastName || 'Student';
    const numbers = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${base}${numbers}!`;
}
```

### 数据保护

- 密码保存在内存中，流程结束后清理
- 截图中可能包含敏感信息，注意保护
- 邮箱验证使用安全的IMAP连接

### 合规使用

- 遵守UNR网站使用条款
- 控制请求频率，避免过于频繁
- 仅用于合法的教育目的

## 性能优化

### 提升速度

```javascript
const config = {
    headless: true,         // 无头模式更快
    timeout: 15000,         // 适当的超时时间
    retryDelay: 3000       // 减少重试延迟
};
```

### 资源管理

```javascript
// 及时关闭浏览器
await browser.close();

// 清理PIN码缓存
pinCodeManager.clearAllPinCodes();
```

## 故障排除

### 常见问题

1. **网站结构变化**
   - 更新选择器配置
   - 检查新的表单字段
   - 调整填写逻辑

2. **邮件接收问题**
   - 检查IMAP配置
   - 确认邮箱权限
   - 查看垃圾邮件文件夹

3. **验证码识别失败**
   - 检查邮件内容格式
   - 更新PIN码提取规则
   - 手动验证邮件内容

### 调试技巧

1. **启用可视模式**
   ```javascript
   const browser = new BrowserController({ headless: false });
   ```

2. **增加等待时间**
   ```javascript
   await browser.sleep(5000);
   ```

3. **查看页面源码**
   ```javascript
   const content = await browser.page.content();
   console.log(content);
   ```

## 最佳实践

### 代码组织

```javascript
// 分离配置
const config = {
    urls: { /* ... */ },
    timeouts: { /* ... */ },
    selectors: { /* ... */ }
};

// 错误处理
try {
    await registrationFlow.execute(studentData);
} catch (error) {
    await handleError(error);
}
```

### 监控和日志

```javascript
// 记录关键步骤
console.log(`📝 开始注册: ${studentData.email}`);

// 保存执行结果
const result = {
    email: studentData.email,
    success: true,
    timestamp: new Date(),
    steps: completedSteps
};
```

## 扩展功能

### 批量注册

```javascript
const students = await excelReader.readAllStudents();
for (const student of students) {
    await registrationFlow.execute(student);
    await delay(5000); // 避免过于频繁
}
```

### 自定义验证

```javascript
class CustomRegistrationFlow extends RegistrationFlow {
    async customValidation(studentData) {
        // 自定义验证逻辑
    }
}
```

## 相关文档

- `browser-automation.md` - 浏览器控制指南
- `pincode-usage.md` - PIN码获取指南
- `email-setup.md` - 邮箱配置指南
