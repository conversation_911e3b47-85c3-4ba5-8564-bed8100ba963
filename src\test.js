/**
 * 测试脚本 - 验证各个模块功能
 * 
 * <AUTHOR> Registration Bot
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { ExcelReader } from './utils/excelReader.js';
import { EmailHandler } from './utils/emailHandler.js';
import { BrowserController } from './automation/browserController.js';

// 获取项目根目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

/**
 * 测试Excel读取功能
 */
async function testExcelReader() {
    console.log('🧪 开始测试Excel读取功能...\n');
    
    try {
        const excelPath = join(projectRoot, 'test.xlsx');
        const reader = new ExcelReader(excelPath);
        
        // 测试读取学生数据
        const studentData = await reader.readStudentData();
        
        console.log('\n📊 Excel读取测试结果:');
        console.log('=====================================');
        console.log('✅ Excel文件读取成功');
        console.log('✅ 学生数据解析成功');
        console.log('\n📋 学生数据详情:');
        console.log(JSON.stringify(studentData, null, 2));
        
        return true;
        
    } catch (error) {
        console.error('\n❌ Excel读取测试失败:');
        console.error(error.message);
        
        // 如果是文件不存在，提供帮助信息
        if (error.message.includes('不存在')) {
            console.log('\n💡 解决方案:');
            console.log('1. 请确保test.xlsx文件存在于项目根目录');
            console.log('2. 检查文件路径是否正确');
            console.log('3. 确保Excel文件格式正确');
        }
        
        return false;
    }
}

/**
 * 测试邮件处理功能
 */
async function testEmailHandler() {
    console.log('\n🧪 邮件处理功能测试...');

    try {
        // 创建邮件处理器
        const emailHandler = new EmailHandler({
            user: '<EMAIL>',
            password: 'pnsbpvwlrvpybiei',
            server: 'imap.qq.com',
            port: 993
        });

        // 测试连接（不等待邮件）
        console.log('📧 测试邮箱连接...');
        const connectionResult = await emailHandler.testConnection();

        if (connectionResult) {
            console.log('✅ 邮箱连接测试成功');
            console.log('💡 邮件模块已就绪，可以接收验证邮件');
            return true;
        } else {
            console.log('❌ 邮箱连接测试失败');
            return false;
        }

    } catch (error) {
        console.error('❌ 邮件处理测试失败:', error.message);

        // 提供故障排除建议
        console.log('\n💡 故障排除建议:');
        console.log('1. 检查网络连接');
        console.log('2. 确认QQ邮箱IMAP服务已开启');
        console.log('3. 验证邮箱密码是否正确（应使用应用专用密码）');
        console.log('4. 检查防火墙设置');

        return false;
    }
}

/**
 * 测试浏览器控制功能
 */
async function testBrowserController() {
    console.log('\n🧪 浏览器控制功能测试...');

    let browser = null;

    try {
        // 创建浏览器控制器
        browser = new BrowserController({
            headless: true, // 测试时使用无头模式
            width: 1280,
            height: 720,
            timeout: 10000
        });

        console.log('🌐 启动浏览器...');
        await browser.launch();

        console.log('🔗 测试页面导航...');
        await browser.navigateTo('https://www.google.com', { screenshot: false });

        console.log('📄 获取页面信息...');
        const title = await browser.getPageTitle();
        const url = await browser.getCurrentUrl();

        if (title && url) {
            console.log(`✅ 页面标题: ${title}`);
            console.log(`✅ 当前URL: ${url}`);
            console.log('✅ 浏览器控制功能测试成功');
            return true;
        } else {
            console.log('❌ 浏览器控制功能测试失败');
            return false;
        }

    } catch (error) {
        console.error('❌ 浏览器控制测试失败:', error.message);

        // 提供故障排除建议
        console.log('\n💡 故障排除建议:');
        console.log('1. 检查Chrome浏览器是否正确安装');
        console.log('2. 确认网络连接正常');
        console.log('3. 尝试关闭防火墙或杀毒软件');
        console.log('4. 检查系统资源是否充足');

        return false;
    } finally {
        // 确保浏览器被关闭
        if (browser) {
            await browser.close();
        }
    }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
    console.log('🚀 UNR自动注册系统 - 模块测试\n');
    console.log('=====================================\n');
    
    const tests = [
        { name: 'Excel读取', func: testExcelReader },
        { name: '邮件处理', func: testEmailHandler },
        { name: '浏览器控制', func: testBrowserController }
    ];
    
    const results = [];
    
    for (const test of tests) {
        try {
            const result = await test.func();
            results.push({ name: test.name, success: result });
        } catch (error) {
            console.error(`❌ ${test.name}测试异常:`, error.message);
            results.push({ name: test.name, success: false, error: error.message });
        }
    }
    
    // 输出测试总结
    console.log('\n\n📊 测试结果总结:');
    console.log('=====================================');
    
    results.forEach(result => {
        const status = result.success ? '✅ 通过' : '❌ 失败';
        console.log(`${result.name}: ${status}`);
        if (result.error) {
            console.log(`   错误: ${result.error}`);
        }
    });
    
    const passedTests = results.filter(r => r.success).length;
    const totalTests = results.length;
    
    console.log(`\n总计: ${passedTests}/${totalTests} 个测试通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！');
    } else {
        console.log('⚠️ 部分测试失败，请检查相关模块');
    }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
    runAllTests().catch(console.error);
}
