/**
 * 邮件功能独立测试脚本
 * 
 * 用于单独测试邮件IMAP功能，不依赖其他模块
 * 
 * <AUTHOR> Registration Bot
 */

import { EmailHandler } from './utils/emailHandler.js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

/**
 * 测试邮箱连接
 */
async function testConnection() {
    console.log('🧪 测试1: 邮箱连接测试');
    console.log('================================');
    
    try {
        const emailHandler = new EmailHandler({
            user: process.env.EMAIL_USER || '<EMAIL>',
            password: process.env.EMAIL_PASSWORD || 'pnsbpvwlrvpybiei',
            server: process.env.EMAIL_SERVER || 'imap.qq.com',
            port: parseInt(process.env.EMAIL_PORT) || 993
        });
        
        const result = await emailHandler.testConnection();
        
        if (result) {
            console.log('✅ 邮箱连接测试成功');
            return true;
        } else {
            console.log('❌ 邮箱连接测试失败');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 连接测试异常:', error.message);
        return false;
    }
}

/**
 * 测试邮件搜索功能
 */
async function testEmailSearch() {
    console.log('\n🧪 测试2: 邮件搜索功能');
    console.log('================================');
    
    try {
        const emailHandler = new EmailHandler({
            user: process.env.EMAIL_USER || '<EMAIL>',
            password: process.env.EMAIL_PASSWORD || 'pnsbpvwlrvpybiei'
        });
        
        await emailHandler.connect();
        await emailHandler.openInbox();
        
        // 搜索最近的邮件
        console.log('🔍 搜索最近的邮件...');
        const recentEmails = await emailHandler.searchVerificationEmails({});
        
        console.log(`📧 找到 ${recentEmails.length} 封邮件`);
        
        if (recentEmails.length > 0) {
            console.log('✅ 邮件搜索功能正常');
        } else {
            console.log('⚠️ 未找到匹配的邮件（这是正常的，如果没有验证邮件）');
        }
        
        await emailHandler.disconnect();
        return true;
        
    } catch (error) {
        console.error('❌ 邮件搜索测试失败:', error.message);
        return false;
    }
}

/**
 * 测试PIN码提取功能
 */
async function testPinExtraction() {
    console.log('\n🧪 测试3: PIN码提取功能');
    console.log('================================');
    
    try {
        const emailHandler = new EmailHandler({});
        
        // 模拟邮件内容
        const mockEmailContent = {
            text: `
            David,

            Thank you for registering online with the University of Nevada, Reno.

            Use the link below to activate your account:

            Authenticate Application

            When requested for a PIN, enter the following temporary PIN:

            *********

            Feel free to contact our Office of Admissions if you need assistance.

            University of Nevada, Reno
            (775) 784-4700, opt. 1 | <EMAIL>
            `,
            html: '<p>PIN: *********</p>'
        };
        
        // 测试PIN码提取
        const pinCode = emailHandler.extractPinCode(mockEmailContent);
        
        if (pinCode === '*********') {
            console.log('✅ PIN码提取功能正常');
            console.log('🔑 提取的PIN码:', pinCode);
            return true;
        } else {
            console.log('❌ PIN码提取失败');
            console.log('期望: *********, 实际:', pinCode);
            return false;
        }
        
    } catch (error) {
        console.error('❌ PIN码提取测试失败:', error.message);
        return false;
    }
}

/**
 * 测试激活链接提取功能
 */
async function testLinkExtraction() {
    console.log('\n🧪 测试4: 激活链接提取功能');
    console.log('================================');
    
    try {
        const emailHandler = new EmailHandler({});
        
        // 模拟邮件内容
        const mockEmailContent = {
            text: `
            Use the link below to activate your account:
            https://admissions.unr.edu/account/activate?token=abc123
            `,
            html: '<a href="https://admissions.unr.edu/account/activate?token=abc123">Activate</a>'
        };
        
        // 测试链接提取
        const activationLink = emailHandler.extractActivationLink(mockEmailContent);
        
        if (activationLink && activationLink.includes('admissions.unr.edu')) {
            console.log('✅ 激活链接提取功能正常');
            console.log('🔗 提取的链接:', activationLink);
            return true;
        } else {
            console.log('❌ 激活链接提取失败');
            console.log('提取结果:', activationLink);
            return false;
        }
        
    } catch (error) {
        console.error('❌ 激活链接提取测试失败:', error.message);
        return false;
    }
}

/**
 * 运行所有邮件测试
 */
async function runEmailTests() {
    console.log('🚀 邮件功能测试套件');
    console.log('=====================================\n');
    
    const tests = [
        { name: '邮箱连接', func: testConnection },
        { name: '邮件搜索', func: testEmailSearch },
        { name: 'PIN码提取', func: testPinExtraction },
        { name: '激活链接提取', func: testLinkExtraction }
    ];
    
    const results = [];
    
    for (const test of tests) {
        try {
            const result = await test.func();
            results.push({ name: test.name, success: result });
        } catch (error) {
            console.error(`❌ ${test.name}测试异常:`, error.message);
            results.push({ name: test.name, success: false, error: error.message });
        }
    }
    
    // 输出测试总结
    console.log('\n📊 邮件测试结果总结:');
    console.log('=====================================');
    
    results.forEach(result => {
        const status = result.success ? '✅ 通过' : '❌ 失败';
        console.log(`${result.name}: ${status}`);
        if (result.error) {
            console.log(`   错误: ${result.error}`);
        }
    });
    
    const passedTests = results.filter(r => r.success).length;
    const totalTests = results.length;
    
    console.log(`\n总计: ${passedTests}/${totalTests} 个邮件测试通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有邮件测试通过！');
    } else {
        console.log('⚠️ 部分邮件测试失败，请检查配置');
        
        // 提供故障排除建议
        console.log('\n💡 故障排除建议:');
        console.log('1. 检查QQ邮箱IMAP服务是否已开启');
        console.log('2. 确认邮箱地址和授权码是否正确');
        console.log('3. 检查网络连接和防火墙设置');
        console.log('4. 参考 email-setup.md 文档进行配置');
    }
    
    return passedTests === totalTests;
}

// 运行邮件测试
if (import.meta.url === `file://${process.argv[1]}`) {
    runEmailTests().catch(console.error);
}

export { runEmailTests };
