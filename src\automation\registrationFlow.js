/**
 * UNR账户注册流程自动化
 * 
 * 功能：
 * 1. 自动填写注册表单
 * 2. 处理邮件验证
 * 3. 完成账户激活
 * 4. 错误处理和重试
 * 
 * <AUTHOR> Registration Bot
 */

import { PinCodeManager } from '../utils/pinCodeManager.js';

/**
 * 注册流程控制器
 */
export class RegistrationFlow {
    constructor(browserController, config = {}) {
        this.browser = browserController;
        this.logger = config.logger || null;
        this.config = {
            registerUrl: config.registerUrl || 'https://admissions.unr.edu/account/register',
            loginUrl: config.loginUrl || 'https://admissions.unr.edu/account/login',
            maxRetries: config.maxRetries || 3,
            retryDelay: config.retryDelay || 5000,
            pinWaitTime: config.pinWaitTime || 300000, // 5分钟等待PIN码
            ...config
        };
        
        this.pinCodeManager = new PinCodeManager();
        this.currentStep = 'initial';
        this.registrationData = null;
    }

    /**
     * 执行完整注册流程
     */
    async execute(studentData) {
        try {
            console.log('\n🚀 开始UNR账户注册流程');
            console.log('=====================================');
            console.log(`👤 学生: ${studentData.firstName} ${studentData.lastName}`);
            console.log(`📧 邮箱: ${studentData.email}`);
            
            this.registrationData = studentData;
            
            // 步骤1: 导航到注册页面
            await this.navigateToRegistrationPage();
            
            // 步骤2: 填写注册表单
            await this.fillRegistrationForm(studentData);
            
            // 步骤3: 提交注册表单
            await this.submitRegistrationForm();
            
            // 步骤4: 处理邮件验证
            await this.handleEmailVerification(studentData);
            
            // 步骤5: 完成账户激活
            await this.completeAccountActivation();
            
            console.log('🎉 UNR账户注册流程完成！');
            return {
                success: true,
                email: studentData.email,
                registrationCompleted: true
            };
            
        } catch (error) {
            console.error('❌ 注册流程失败:', error.message);
            await this.handleRegistrationError(error);
            throw error;
        }
    }

    /**
     * 步骤1: 导航到注册页面
     */
    async navigateToRegistrationPage() {
        try {
            this.currentStep = 'navigation';
            console.log('\n📍 步骤1: 导航到注册页面');

            if (this.logger) {
                this.logger.logStepStart('Navigate to Registration Page', 1, {
                    url: this.config.registerUrl
                });
            }

            await this.browser.navigateTo(this.config.registerUrl);

            // 等待页面加载完成
            await this.browser.waitForLoadState('domcontentloaded');

            // 检查是否成功到达注册页面
            const title = await this.browser.getPageTitle();
            console.log(`📄 页面标题: ${title}`);

            if (title && title.toLowerCase().includes('register')) {
                console.log('✅ 成功到达注册页面');
                if (this.logger) {
                    this.logger.logRegistration('info', 'Successfully reached registration page', {
                        title,
                        url: this.config.registerUrl
                    });
                }
            } else {
                console.log('⚠️ 页面标题不包含"register"，但继续执行');
                if (this.logger) {
                    this.logger.logRegistration('warn', 'Page title does not contain "register"', {
                        title,
                        url: this.config.registerUrl
                    });
                }
            }

            // 截图记录
            await this.browser.takeScreenshot('01-registration-page');

            if (this.logger) {
                this.logger.logStepEnd('Navigate to Registration Page', 1, {
                    success: true,
                    pageTitle: title
                });
            }

        } catch (error) {
            console.error('❌ 导航到注册页面失败:', error.message);
            if (this.logger) {
                this.logger.logError(error, {
                    step: 'navigation',
                    stepNumber: 1,
                    url: this.config.registerUrl
                });
            }
            throw new Error(`导航失败: ${error.message}`);
        }
    }

    /**
     * 步骤2: 填写注册表单
     */
    async fillRegistrationForm(studentData) {
        try {
            this.currentStep = 'form-filling';
            console.log('\n✏️ 步骤2: 填写注册表单');
            
            // 等待表单元素加载
            console.log('⏳ 等待注册表单加载...');
            await this.browser.waitForElement('form', { timeout: 10000 });
            
            // 填写邮箱地址
            console.log('📧 填写邮箱地址...');
            const emailSelectors = [
                'input[name="email"]',
                'input[type="email"]',
                '#email',
                '#Email',
                'input[placeholder*="email" i]'
            ];
            await this.fillFieldWithSelectors(emailSelectors, studentData.email, '邮箱');
            
            // 填写名字
            console.log('👤 填写名字...');
            const firstNameSelectors = [
                'input[name="firstName"]',
                'input[name="first_name"]',
                'input[name="FirstName"]',
                '#firstName',
                '#first-name',
                'input[placeholder*="first" i]'
            ];
            await this.fillFieldWithSelectors(firstNameSelectors, studentData.firstName, '名字');
            
            // 填写姓氏
            console.log('👤 填写姓氏...');
            const lastNameSelectors = [
                'input[name="lastName"]',
                'input[name="last_name"]',
                'input[name="LastName"]',
                '#lastName',
                '#last-name',
                'input[placeholder*="last" i]'
            ];
            await this.fillFieldWithSelectors(lastNameSelectors, studentData.lastName, '姓氏');
            
            // 填写生日（如果需要）
            if (studentData.birthDate) {
                console.log('📅 填写生日...');
                await this.fillBirthDate(studentData.birthDate);
            }
            
            // 填写密码（生成或使用默认密码）
            console.log('🔐 设置密码...');
            const password = this.generatePassword(studentData);
            await this.fillPassword(password);
            
            // 同意条款（如果有）
            console.log('📋 同意使用条款...');
            await this.acceptTermsAndConditions();
            
            console.log('✅ 注册表单填写完成');
            
            // 截图记录
            await this.browser.takeScreenshot('02-form-filled');
            
        } catch (error) {
            console.error('❌ 填写注册表单失败:', error.message);
            await this.browser.takeScreenshot('02-form-error');
            throw new Error(`表单填写失败: ${error.message}`);
        }
    }

    /**
     * 使用多个选择器尝试填写字段
     */
    async fillFieldWithSelectors(selectors, value, fieldName) {
        for (const selector of selectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.fillInput(selector, value);
                console.log(`✅ ${fieldName}填写成功: ${selector}`);
                return;
            } catch (error) {
                // 继续尝试下一个选择器
                continue;
            }
        }
        
        console.warn(`⚠️ 未找到${fieldName}字段，跳过`);
    }

    /**
     * 填写生日信息
     */
    async fillBirthDate(birthDate) {
        try {
            // 尝试不同的生日输入格式
            const birthDateSelectors = [
                'input[name="birthDate"]',
                'input[name="dateOfBirth"]',
                'input[type="date"]',
                '#birthDate',
                '#birth-date'
            ];
            
            // 格式化生日 (假设输入格式为 YYYY-MM-DD)
            const formattedDate = this.formatBirthDate(birthDate);
            
            for (const selector of birthDateSelectors) {
                try {
                    await this.browser.waitForElement(selector, { timeout: 3000 });
                    await this.browser.fillInput(selector, formattedDate);
                    console.log(`✅ 生日填写成功: ${formattedDate}`);
                    return;
                } catch (error) {
                    continue;
                }
            }
            
            console.log('⚠️ 未找到生日字段，跳过');
            
        } catch (error) {
            console.warn('⚠️ 生日填写失败，跳过:', error.message);
        }
    }

    /**
     * 填写密码
     */
    async fillPassword(password) {
        try {
            const passwordSelectors = [
                'input[name="password"]',
                'input[type="password"]',
                '#password',
                '#Password'
            ];
            
            const confirmPasswordSelectors = [
                'input[name="confirmPassword"]',
                'input[name="confirm_password"]',
                'input[name="passwordConfirm"]',
                '#confirmPassword',
                '#confirm-password'
            ];
            
            // 填写密码
            await this.fillFieldWithSelectors(passwordSelectors, password, '密码');
            
            // 填写确认密码
            await this.fillFieldWithSelectors(confirmPasswordSelectors, password, '确认密码');
            
            // 保存密码到学生数据中
            this.registrationData.password = password;
            
        } catch (error) {
            console.error('❌ 密码填写失败:', error.message);
            throw error;
        }
    }

    /**
     * 同意条款和条件
     */
    async acceptTermsAndConditions() {
        try {
            const termsSelectors = [
                'input[type="checkbox"][name*="terms"]',
                'input[type="checkbox"][name*="agree"]',
                'input[type="checkbox"][name*="accept"]',
                '#terms',
                '#agree',
                '#accept-terms'
            ];
            
            for (const selector of termsSelectors) {
                try {
                    await this.browser.waitForElement(selector, { timeout: 3000 });
                    await this.browser.clickElement(selector);
                    console.log('✅ 已同意使用条款');
                    return;
                } catch (error) {
                    continue;
                }
            }
            
            console.log('⚠️ 未找到条款复选框，跳过');
            
        } catch (error) {
            console.warn('⚠️ 条款同意失败，跳过:', error.message);
        }
    }

    /**
     * 步骤3: 提交注册表单
     */
    async submitRegistrationForm() {
        try {
            this.currentStep = 'form-submission';
            console.log('\n📤 步骤3: 提交注册表单');
            
            // 查找提交按钮
            const submitSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("Register")',
                'button:contains("Create Account")',
                'button:contains("Sign Up")',
                '#submit',
                '#register-btn',
                '.submit-btn'
            ];
            
            let submitted = false;
            for (const selector of submitSelectors) {
                try {
                    await this.browser.waitForElement(selector, { timeout: 3000 });
                    
                    console.log(`🖱️ 点击提交按钮: ${selector}`);
                    await this.browser.clickElement(selector);
                    
                    submitted = true;
                    break;
                } catch (error) {
                    continue;
                }
            }
            
            if (!submitted) {
                throw new Error('未找到提交按钮');
            }
            
            // 等待页面响应
            console.log('⏳ 等待页面响应...');
            await this.browser.sleep(3000);
            
            // 截图记录
            await this.browser.takeScreenshot('03-form-submitted');
            
            console.log('✅ 注册表单提交成功');
            
        } catch (error) {
            console.error('❌ 提交注册表单失败:', error.message);
            await this.browser.takeScreenshot('03-submit-error');
            throw new Error(`表单提交失败: ${error.message}`);
        }
    }

    /**
     * 步骤4: 处理邮件验证
     */
    async handleEmailVerification(studentData) {
        try {
            this.currentStep = 'email-verification';
            console.log('\n📧 步骤4: 处理邮件验证');
            
            console.log('⏳ 等待验证邮件...');
            console.log(`📧 监听邮箱: ${studentData.email}`);
            
            // 获取PIN码
            const pinCode = await this.pinCodeManager.getPinCodeFromStudentData(studentData, {
                maxWaitTime: this.config.pinWaitTime
            });
            
            console.log(`🔑 收到PIN码: ${pinCode}`);
            
            // 查找PIN码输入框
            console.log('🔍 查找PIN码输入框...');
            const pinInputSelectors = [
                'input[name="pin"]',
                'input[name="PIN"]',
                'input[name="code"]',
                'input[name="verificationCode"]',
                '#pin',
                '#PIN',
                '#code',
                '#verification-code'
            ];
            
            await this.fillFieldWithSelectors(pinInputSelectors, pinCode, 'PIN码');
            
            // 提交PIN码
            console.log('📤 提交PIN码...');
            const pinSubmitSelectors = [
                'button[type="submit"]',
                'button:contains("Verify")',
                'button:contains("Activate")',
                'button:contains("Continue")',
                '#verify-btn',
                '#activate-btn'
            ];
            
            for (const selector of pinSubmitSelectors) {
                try {
                    await this.browser.waitForElement(selector, { timeout: 3000 });
                    await this.browser.clickElement(selector);
                    console.log('✅ PIN码提交成功');
                    break;
                } catch (error) {
                    continue;
                }
            }
            
            // 等待验证完成
            await this.browser.sleep(3000);
            
            // 截图记录
            await this.browser.takeScreenshot('04-pin-verified');
            
            console.log('✅ 邮件验证完成');
            
        } catch (error) {
            console.error('❌ 邮件验证失败:', error.message);
            await this.browser.takeScreenshot('04-verification-error');
            throw new Error(`邮件验证失败: ${error.message}`);
        }
    }

    /**
     * 步骤5: 完成账户激活并自动登录
     */
    async completeAccountActivation() {
        try {
            this.currentStep = 'activation';
            console.log('\n🎯 步骤5: 完成账户激活并自动登录');

            // 等待PIN码验证完成
            await this.browser.sleep(3000);

            // 检查是否有成功消息或自动跳转
            const successSelectors = [
                '.success',
                '.alert-success',
                '.message-success',
                '[class*="success"]',
                ':contains("success")',
                ':contains("activated")',
                ':contains("verified")'
            ];

            let activationSuccess = false;
            for (const selector of successSelectors) {
                try {
                    await this.browser.waitForElement(selector, { timeout: 5000 });
                    console.log('✅ 找到成功消息');
                    activationSuccess = true;
                    break;
                } catch (error) {
                    continue;
                }
            }

            // 获取当前页面信息
            const currentUrl = await this.browser.getCurrentUrl();
            const pageTitle = await this.browser.getPageTitle();

            console.log(`📄 当前页面: ${pageTitle}`);
            console.log(`🔗 当前URL: ${currentUrl}`);

            // 检查是否已经自动登录到申请页面
            if (currentUrl.includes('apply') || currentUrl.includes('application')) {
                console.log('🎉 账户激活成功，已自动进入申请页面！');
                await this.browser.takeScreenshot('05-auto-login-success');
                return {
                    activated: true,
                    autoLoggedIn: true,
                    currentUrl: currentUrl
                };
            }

            // 如果没有自动跳转，尝试手动登录
            if (currentUrl.includes('login') || pageTitle.toLowerCase().includes('login')) {
                console.log('🔐 检测到登录页面，尝试自动登录...');
                await this.performAutoLogin();

                // 登录后检查是否进入申请页面
                const newUrl = await this.browser.getCurrentUrl();
                if (newUrl.includes('apply') || newUrl.includes('application')) {
                    console.log('✅ 自动登录成功，已进入申请页面');
                    return {
                        activated: true,
                        autoLoggedIn: true,
                        currentUrl: newUrl
                    };
                }
            }

            // 截图记录
            await this.browser.takeScreenshot('05-activation-complete');

            if (activationSuccess) {
                console.log('✅ 账户激活成功');
            } else {
                console.log('⚠️ 无法确认激活状态，但流程已完成');
            }

            return {
                activated: true,
                autoLoggedIn: false,
                currentUrl: currentUrl
            };

        } catch (error) {
            console.error('❌ 账户激活检查失败:', error.message);
            await this.browser.takeScreenshot('05-activation-error');
            // 不抛出错误，因为这可能只是检查失败
            console.log('⚠️ 激活检查失败，但注册流程可能已完成');

            return {
                activated: false,
                autoLoggedIn: false,
                error: error.message
            };
        }
    }

    /**
     * 执行自动登录
     */
    async performAutoLogin() {
        try {
            console.log('🔐 执行自动登录...');

            // 填写邮箱
            const emailSelectors = [
                'input[name="email"]',
                'input[type="email"]',
                '#email',
                '#Email'
            ];

            for (const selector of emailSelectors) {
                try {
                    await this.browser.waitForElement(selector, { timeout: 3000 });
                    await this.browser.fillInput(selector, this.registrationData.email);
                    console.log('✅ 邮箱填写成功');
                    break;
                } catch (error) {
                    continue;
                }
            }

            // 填写密码
            const passwordSelectors = [
                'input[name="password"]',
                'input[type="password"]',
                '#password',
                '#Password'
            ];

            for (const selector of passwordSelectors) {
                try {
                    await this.browser.waitForElement(selector, { timeout: 3000 });
                    await this.browser.fillInput(selector, this.registrationData.password);
                    console.log('✅ 密码填写成功');
                    break;
                } catch (error) {
                    continue;
                }
            }

            // 点击登录按钮
            const loginSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("Login")',
                'button:contains("Sign In")',
                '#login-btn',
                '#signin-btn'
            ];

            for (const selector of loginSelectors) {
                try {
                    await this.browser.waitForElement(selector, { timeout: 3000 });
                    await this.browser.clickElement(selector);
                    console.log('✅ 登录按钮点击成功');
                    break;
                } catch (error) {
                    continue;
                }
            }

            // 等待登录完成
            await this.browser.sleep(5000);

            console.log('✅ 自动登录完成');

        } catch (error) {
            console.error('❌ 自动登录失败:', error.message);
            throw error;
        }
    }

    /**
     * 处理注册错误
     */
    async handleRegistrationError(error) {
        console.error(`❌ 注册流程在步骤"${this.currentStep}"失败:`, error.message);
        
        // 截图记录错误状态
        await this.browser.takeScreenshot(`error-${this.currentStep}`);
        
        // 获取页面信息用于调试
        try {
            const url = await this.browser.getCurrentUrl();
            const title = await this.browser.getPageTitle();
            console.log(`🔍 错误发生时的页面信息:`);
            console.log(`   URL: ${url}`);
            console.log(`   标题: ${title}`);
        } catch (e) {
            console.log('无法获取页面信息');
        }
    }

    /**
     * 生成密码
     */
    generatePassword(studentData) {
        // 生成基于学生信息的密码
        const base = studentData.lastName || 'Student';
        const numbers = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `${base}${numbers}!`;
    }

    /**
     * 格式化生日
     */
    formatBirthDate(birthDate) {
        try {
            if (typeof birthDate === 'string') {
                // 假设输入格式为 YYYY-MM-DD 或类似格式
                return birthDate;
            }
            
            // 如果是Date对象，转换为字符串
            if (birthDate instanceof Date) {
                return birthDate.toISOString().split('T')[0];
            }
            
            return birthDate.toString();
        } catch (error) {
            console.warn('生日格式化失败:', error.message);
            return '1995-01-01'; // 默认生日
        }
    }
}
