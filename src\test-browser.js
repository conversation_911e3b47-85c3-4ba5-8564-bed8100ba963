/**
 * 浏览器控制功能测试脚本
 * 
 * 测试Puppeteer浏览器自动化功能
 * 
 * <AUTHOR> Registration Bot
 */

import { BrowserController } from './automation/browserController.js';

/**
 * 测试浏览器启动和关闭
 */
async function testBrowserLaunchAndClose() {
    console.log('🧪 测试1: 浏览器启动和关闭');
    console.log('================================');
    
    let browser = null;
    
    try {
        browser = new BrowserController({
            headless: true,
            timeout: 10000
        });
        
        console.log('🌐 启动浏览器...');
        await browser.launch();
        
        if (browser.isReady()) {
            console.log('✅ 浏览器启动成功');
            
            console.log('🔒 关闭浏览器...');
            await browser.close();
            
            if (!browser.isReady()) {
                console.log('✅ 浏览器关闭成功');
                return true;
            } else {
                console.log('❌ 浏览器关闭失败');
                return false;
            }
        } else {
            console.log('❌ 浏览器启动失败');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 浏览器启动/关闭测试失败:', error.message);
        return false;
    } finally {
        if (browser && browser.isReady()) {
            await browser.close();
        }
    }
}

/**
 * 测试页面导航功能
 */
async function testPageNavigation() {
    console.log('\n🧪 测试2: 页面导航功能');
    console.log('================================');
    
    let browser = null;
    
    try {
        browser = new BrowserController({
            headless: true,
            timeout: 15000
        });
        
        await browser.launch();
        
        console.log('🔗 导航到Google首页...');
        await browser.navigateTo('https://www.google.com', { screenshot: false });
        
        const title = await browser.getPageTitle();
        const url = await browser.getCurrentUrl();
        
        if (title && url.includes('google.com')) {
            console.log(`✅ 页面导航成功 - 标题: ${title}`);
            return true;
        } else {
            console.log('❌ 页面导航失败');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 页面导航测试失败:', error.message);
        return false;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

/**
 * 测试元素交互功能
 */
async function testElementInteraction() {
    console.log('\n🧪 测试3: 元素交互功能');
    console.log('================================');
    
    let browser = null;
    
    try {
        browser = new BrowserController({
            headless: true,
            timeout: 15000
        });
        
        await browser.launch();
        
        console.log('🔗 导航到Google首页...');
        await browser.navigateTo('https://www.google.com', { screenshot: false });
        
        console.log('🔍 查找搜索框...');
        const searchBoxSelector = 'textarea[name="q"], input[name="q"]';
        await browser.waitForElement(searchBoxSelector);
        
        console.log('✏️ 填写搜索框...');
        await browser.fillInput(searchBoxSelector, 'University of Nevada Reno');
        
        console.log('👆 点击搜索按钮...');
        const searchButtonSelector = 'input[type="submit"], button[type="submit"]';
        
        try {
            await browser.clickElement(searchButtonSelector);
            console.log('✅ 元素交互测试成功');
            return true;
        } catch (clickError) {
            // 如果点击搜索按钮失败，尝试按回车键
            console.log('⌨️ 尝试按回车键搜索...');
            await browser.page.keyboard.press('Enter');
            console.log('✅ 元素交互测试成功（使用回车键）');
            return true;
        }
        
    } catch (error) {
        console.error('❌ 元素交互测试失败:', error.message);
        return false;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

/**
 * 测试UNR网站访问
 */
async function testUNRWebsiteAccess() {
    console.log('\n🧪 测试4: UNR网站访问');
    console.log('================================');
    
    let browser = null;
    
    try {
        browser = new BrowserController({
            headless: true,
            timeout: 20000
        });
        
        await browser.launch();
        
        console.log('🔗 访问UNR招生网站...');
        await browser.navigateTo('https://admissions.unr.edu/', { screenshot: false });
        
        const title = await browser.getPageTitle();
        const url = await browser.getCurrentUrl();
        
        if (title && url.includes('unr.edu')) {
            console.log(`✅ UNR网站访问成功 - 标题: ${title}`);
            
            // 尝试查找注册链接
            console.log('🔍 查找注册相关链接...');
            try {
                const registerSelectors = [
                    'a[href*="register"]',
                    'a[href*="account"]',
                    'a:contains("Register")',
                    'a:contains("Create Account")'
                ];
                
                for (const selector of registerSelectors) {
                    try {
                        await browser.waitForElement(selector, { timeout: 3000 });
                        console.log(`✅ 找到注册链接: ${selector}`);
                        break;
                    } catch (e) {
                        // 继续尝试下一个选择器
                    }
                }
                
            } catch (error) {
                console.log('⚠️ 未找到注册链接（页面结构可能已变化）');
            }
            
            return true;
        } else {
            console.log('❌ UNR网站访问失败');
            return false;
        }
        
    } catch (error) {
        console.error('❌ UNR网站访问测试失败:', error.message);
        console.log('💡 这可能是网络问题或网站暂时不可用');
        return false;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

/**
 * 测试截图功能
 */
async function testScreenshotFunction() {
    console.log('\n🧪 测试5: 截图功能');
    console.log('================================');
    
    let browser = null;
    
    try {
        browser = new BrowserController({
            headless: true,
            timeout: 10000
        });
        
        await browser.launch();
        
        console.log('🔗 导航到测试页面...');
        await browser.navigateTo('https://www.google.com', { screenshot: false });
        
        console.log('📸 测试截图功能...');
        const screenshotPath = await browser.takeScreenshot('test-screenshot');
        
        if (screenshotPath) {
            console.log(`✅ 截图功能测试成功 - 文件: ${screenshotPath}`);
            return true;
        } else {
            console.log('❌ 截图功能测试失败');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 截图功能测试失败:', error.message);
        return false;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

/**
 * 运行所有浏览器测试
 */
async function runBrowserTests() {
    console.log('🚀 Puppeteer浏览器控制测试套件');
    console.log('=====================================\n');
    
    const tests = [
        { name: '浏览器启动关闭', func: testBrowserLaunchAndClose },
        { name: '页面导航', func: testPageNavigation },
        { name: '元素交互', func: testElementInteraction },
        { name: 'UNR网站访问', func: testUNRWebsiteAccess },
        { name: '截图功能', func: testScreenshotFunction }
    ];
    
    const results = [];
    
    for (const test of tests) {
        try {
            const result = await test.func();
            results.push({ name: test.name, success: result });
        } catch (error) {
            console.error(`❌ ${test.name}测试异常:`, error.message);
            results.push({ name: test.name, success: false, error: error.message });
        }
    }
    
    // 输出测试总结
    console.log('\n📊 浏览器测试结果总结:');
    console.log('=====================================');
    
    results.forEach(result => {
        const status = result.success ? '✅ 通过' : '❌ 失败';
        console.log(`${result.name}: ${status}`);
        if (result.error) {
            console.log(`   错误: ${result.error}`);
        }
    });
    
    const passedTests = results.filter(r => r.success).length;
    const totalTests = results.length;
    
    console.log(`\n总计: ${passedTests}/${totalTests} 个浏览器测试通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有浏览器测试通过！');
    } else {
        console.log('⚠️ 部分浏览器测试失败，请检查环境配置');
        
        // 提供故障排除建议
        console.log('\n💡 故障排除建议:');
        console.log('1. 确认Chrome浏览器已正确安装');
        console.log('2. 检查网络连接是否正常');
        console.log('3. 尝试关闭防火墙或杀毒软件');
        console.log('4. 确保系统有足够的内存和CPU资源');
        console.log('5. 如果是Linux系统，可能需要安装额外的依赖包');
    }
    
    return passedTests === totalTests;
}

// 运行浏览器测试
if (import.meta.url === `file://${process.argv[1]}`) {
    runBrowserTests().catch(console.error);
}

export { runBrowserTests };
