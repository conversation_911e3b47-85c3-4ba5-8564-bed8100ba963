/**
 * Puppeteer浏览器控制模块
 * 
 * 功能：
 * 1. 浏览器启动和管理
 * 2. 页面导航和操作
 * 3. 元素等待和交互
 * 4. 截图和调试支持
 * 
 * <AUTHOR> Registration Bot
 */

import puppeteer from 'puppeteer';
import fs from 'fs-extra';
import path from 'path';

/**
 * 浏览器控制器类
 */
export class BrowserController {
    constructor(config = {}) {
        this.config = {
            headless: config.headless || false,
            viewport: {
                width: config.width || 1280,
                height: config.height || 720
            },
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ],
            defaultTimeout: config.timeout || 30000,
            screenshotPath: config.screenshotPath || './screenshots'
        };
        
        this.browser = null;
        this.page = null;
        this.isLaunched = false;
        this.screenshotCounter = 0;
    }

    /**
     * 启动浏览器
     */
    async launch() {
        try {
            console.log('🌐 正在启动浏览器...');
            
            this.browser = await puppeteer.launch({
                headless: this.config.headless,
                args: this.config.args,
                defaultViewport: this.config.viewport
            });
            
            this.page = await this.browser.newPage();
            
            // 设置默认超时
            this.page.setDefaultTimeout(this.config.defaultTimeout);
            this.page.setDefaultNavigationTimeout(this.config.defaultTimeout);
            
            // 设置用户代理
            await this.page.setUserAgent(
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            );
            
            // 创建截图目录
            await fs.ensureDir(this.config.screenshotPath);
            
            this.isLaunched = true;
            console.log('✅ 浏览器启动成功');
            
            return this.browser;
            
        } catch (error) {
            console.error('❌ 浏览器启动失败:', error.message);
            throw error;
        }
    }

    /**
     * 导航到指定URL
     */
    async navigateTo(url, options = {}) {
        try {
            console.log(`🔗 导航到: ${url}`);
            
            if (!this.page) {
                throw new Error('浏览器未启动，请先调用launch()');
            }
            
            const response = await this.page.goto(url, {
                waitUntil: options.waitUntil || 'networkidle2',
                timeout: options.timeout || this.config.defaultTimeout
            });
            
            // 等待页面加载完成
            await this.page.waitForLoadState('domcontentloaded');
            
            console.log('✅ 页面加载完成');
            
            // 自动截图（如果启用调试模式）
            if (options.screenshot !== false) {
                await this.takeScreenshot(`navigate-${this.getUrlSlug(url)}`);
            }
            
            return response;
            
        } catch (error) {
            console.error(`❌ 导航失败: ${error.message}`);
            await this.takeScreenshot('navigation-error');
            throw error;
        }
    }

    /**
     * 等待元素出现
     */
    async waitForElement(selector, options = {}) {
        try {
            console.log(`⏳ 等待元素: ${selector}`);
            
            const element = await this.page.waitForSelector(selector, {
                timeout: options.timeout || this.config.defaultTimeout,
                visible: options.visible !== false
            });
            
            console.log(`✅ 元素已找到: ${selector}`);
            return element;
            
        } catch (error) {
            console.error(`❌ 等待元素失败: ${selector} - ${error.message}`);
            await this.takeScreenshot(`wait-element-error-${this.getSelectorSlug(selector)}`);
            throw error;
        }
    }

    /**
     * 点击元素
     */
    async clickElement(selector, options = {}) {
        try {
            console.log(`👆 点击元素: ${selector}`);
            
            // 等待元素出现
            await this.waitForElement(selector, options);
            
            // 滚动到元素位置
            await this.page.evaluate((sel) => {
                const element = document.querySelector(sel);
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }, selector);
            
            // 等待一下确保滚动完成
            await this.sleep(500);
            
            // 点击元素
            await this.page.click(selector);
            
            console.log(`✅ 元素点击成功: ${selector}`);
            
            // 等待页面响应
            if (options.waitAfterClick !== false) {
                await this.sleep(1000);
            }
            
        } catch (error) {
            console.error(`❌ 点击元素失败: ${selector} - ${error.message}`);
            await this.takeScreenshot(`click-error-${this.getSelectorSlug(selector)}`);
            throw error;
        }
    }

    /**
     * 填写输入框
     */
    async fillInput(selector, value, options = {}) {
        try {
            console.log(`✏️ 填写输入框: ${selector} = ${value}`);
            
            // 等待元素出现
            await this.waitForElement(selector, options);
            
            // 清空输入框
            await this.page.click(selector, { clickCount: 3 });
            await this.page.keyboard.press('Delete');
            
            // 输入值
            await this.page.type(selector, value, {
                delay: options.delay || 50 // 模拟真实输入速度
            });
            
            console.log(`✅ 输入框填写成功: ${selector}`);
            
        } catch (error) {
            console.error(`❌ 填写输入框失败: ${selector} - ${error.message}`);
            await this.takeScreenshot(`fill-error-${this.getSelectorSlug(selector)}`);
            throw error;
        }
    }

    /**
     * 选择下拉框选项
     */
    async selectOption(selector, value, options = {}) {
        try {
            console.log(`📋 选择选项: ${selector} = ${value}`);
            
            // 等待元素出现
            await this.waitForElement(selector, options);
            
            // 选择选项
            await this.page.select(selector, value);
            
            console.log(`✅ 选项选择成功: ${selector} = ${value}`);
            
        } catch (error) {
            console.error(`❌ 选择选项失败: ${selector} - ${error.message}`);
            await this.takeScreenshot(`select-error-${this.getSelectorSlug(selector)}`);
            throw error;
        }
    }

    /**
     * 等待页面加载状态
     */
    async waitForLoadState(state = 'domcontentloaded', timeout = null) {
        try {
            console.log(`⏳ 等待页面状态: ${state}`);
            
            await this.page.waitForLoadState(state, {
                timeout: timeout || this.config.defaultTimeout
            });
            
            console.log(`✅ 页面状态就绪: ${state}`);
            
        } catch (error) {
            console.error(`❌ 等待页面状态失败: ${state} - ${error.message}`);
            throw error;
        }
    }

    /**
     * 截图
     */
    async takeScreenshot(name = null, options = {}) {
        try {
            if (!this.page) {
                return null;
            }
            
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = name ? 
                `${timestamp}-${name}.png` : 
                `${timestamp}-screenshot-${++this.screenshotCounter}.png`;
            
            const filepath = path.join(this.config.screenshotPath, filename);
            
            await this.page.screenshot({
                path: filepath,
                fullPage: options.fullPage || false,
                ...options
            });
            
            console.log(`📸 截图已保存: ${filename}`);
            return filepath;
            
        } catch (error) {
            console.error('❌ 截图失败:', error.message);
            return null;
        }
    }

    /**
     * 获取页面标题
     */
    async getPageTitle() {
        try {
            const title = await this.page.title();
            console.log(`📄 页面标题: ${title}`);
            return title;
        } catch (error) {
            console.error('❌ 获取页面标题失败:', error.message);
            return null;
        }
    }

    /**
     * 获取当前URL
     */
    async getCurrentUrl() {
        try {
            const url = this.page.url();
            console.log(`🔗 当前URL: ${url}`);
            return url;
        } catch (error) {
            console.error('❌ 获取当前URL失败:', error.message);
            return null;
        }
    }

    /**
     * 等待导航完成
     */
    async waitForNavigation(options = {}) {
        try {
            console.log('⏳ 等待页面导航...');
            
            await this.page.waitForNavigation({
                waitUntil: options.waitUntil || 'networkidle2',
                timeout: options.timeout || this.config.defaultTimeout
            });
            
            console.log('✅ 页面导航完成');
            
        } catch (error) {
            console.error('❌ 等待导航失败:', error.message);
            throw error;
        }
    }

    /**
     * 关闭浏览器
     */
    async close() {
        try {
            if (this.browser) {
                console.log('🔒 正在关闭浏览器...');
                await this.browser.close();
                this.browser = null;
                this.page = null;
                this.isLaunched = false;
                console.log('✅ 浏览器已关闭');
            }
        } catch (error) {
            console.error('❌ 关闭浏览器失败:', error.message);
        }
    }

    /**
     * 工具方法：延时
     */
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 工具方法：获取URL简化名称
     */
    getUrlSlug(url) {
        return url.replace(/[^a-zA-Z0-9]/g, '-').substring(0, 50);
    }

    /**
     * 工具方法：获取选择器简化名称
     */
    getSelectorSlug(selector) {
        return selector.replace(/[^a-zA-Z0-9]/g, '-').substring(0, 30);
    }

    /**
     * 检查浏览器状态
     */
    isReady() {
        return this.isLaunched && this.browser && this.page;
    }
}
