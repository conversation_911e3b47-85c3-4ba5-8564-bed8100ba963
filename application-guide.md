# UNR申请表单自动化指南

## 功能概述

申请表单自动化模块实现了完整的UNR申请流程，包含10个步骤的自动化填写和提交：

1. **Application Information** - 申请信息
2. **Nondegree Application Instructions** - 非学位申请说明
3. **Application Term** - 申请学期
4. **Personal Background** - 个人背景
5. **Background Continued** - 背景信息续
6. **Emergency Contact** - 紧急联系人
7. **Academic History** - 学术历史
8. **Program Selection** - 项目选择
9. **Signature** - 签名
10. **Review** - 审核提交

## 申请流程架构

### 完整流程图

```
登录系统 → 步骤1-10 → 最终提交 → 申请完成
```

### 详细步骤说明

#### 步骤1: Application Information
- 选择申请类型（本科/研究生）
- 确认个人基本信息
- 验证申请资格

#### 步骤2: Nondegree Application Instructions
- 阅读申请说明和要求
- 确认理解申请流程
- 同意申请条款

#### 步骤3: Application Term
- 选择申请入学学期
- 选择入学类型（首次入学/转学）
- 确认申请时间

#### 步骤4: Personal Background
- 填写生日信息
- 选择性别
- 填写社会安全号(SSN)
- 填写完整地址信息

#### 步骤5: Background Continued
- 填写联系电话
- 选择公民身份状态
- 选择种族/民族信息

#### 步骤6: Emergency Contact
- 填写紧急联系人姓名
- 选择联系人关系
- 填写联系人邮箱
- 选择联系语言

#### 步骤7: Academic History
- 填写高中学校信息
- 选择就读起止时间
- 确认毕业状态

#### 步骤8: Program Selection
- 选择申请学院
- 选择专业方向
- 选择学位类型

#### 步骤9: Signature
- 电子签名确认
- 填写签名日期
- 同意申请条款

#### 步骤10: Review
- 审核所有申请信息
- 确认信息准确性
- 最终提交申请

## 使用方法

### 基本用法

```javascript
import { ApplicationFlow } from './automation/applicationFlow.js';
import { BrowserController } from './automation/browserController.js';

// 创建浏览器控制器
const browser = new BrowserController({
    headless: false,
    timeout: 30000
});

await browser.launch();

// 创建申请流程控制器
const applicationFlow = new ApplicationFlow(browser);

// 执行申请流程
const result = await applicationFlow.execute(studentData);
console.log('申请结果:', result);

await browser.close();
```

### 集成使用

```javascript
import { UNRAutoRegistration } from './main.js';

// 使用主程序执行完整流程（注册+申请）
const app = new UNRAutoRegistration();
await app.run();
```

## 配置选项

### 申请流程配置

```javascript
const config = {
    applyUrl: 'https://admissions.unr.edu/apply/',
    loginUrl: 'https://admissions.unr.edu/account/login',
    maxRetries: 3,              // 最大重试次数
    stepDelay: 2000             // 步骤间延迟（毫秒）
};
```

### 学生数据要求

```javascript
const studentData = {
    // 必填字段
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Smith',
    password: 'generated_password',
    
    // 个人信息
    birthDate: '1995-05-15',
    gender: 'Male',
    ssn: '*********',
    phone: '(*************',
    
    // 地址信息
    address: {
        street: '123 Main St',
        city: 'Las Vegas',
        state: 'NV',
        zipCode: '89101'
    },
    
    // 高中信息
    highSchool: {
        schoolName: 'Las Vegas High School',
        startDate: { month: 'August', year: '2020' },
        endDate: { month: 'June', year: '2024' },
        graduated: true
    },
    
    // 紧急联系人
    emergencyContact: {
        firstName: 'Jane',
        lastName: 'Smith',
        relationship: 'parent',
        email: '<EMAIL>',
        language: 'English'
    }
};
```

## 智能表单处理

### 多选择器策略

每个表单字段都使用多个选择器，确保在网站结构变化时仍能正常工作：

```javascript
// 邮箱字段选择器
const emailSelectors = [
    'input[name="email"]',      // name属性
    'input[type="email"]',      // type属性
    '#email',                   // ID选择器
    '#Email'                    // 大小写变体
];
```

### 自适应填写

系统会根据页面结构自动调整填写策略：
- 优先使用最稳定的选择器
- 自动跳过不存在的字段
- 使用默认值填充缺失数据

## 错误处理机制

### 步骤级错误处理

每个步骤都有独立的错误处理：
- 自动截图保存错误状态
- 记录错误发生的具体步骤
- 提供详细的错误信息

### 流程级错误恢复

- 记录已完成的步骤
- 支持从失败点重新开始
- 自动重试机制

### 调试支持

```javascript
// 启用调试模式
const browser = new BrowserController({
    headless: false,    // 显示浏览器
    timeout: 60000      // 增加超时时间
});

// 查看截图
// 所有步骤都会自动截图保存在 screenshots/ 目录
```

## 测试验证

### 运行测试

```bash
# 测试申请流程
npm run test:application

# 测试所有功能
npm test
```

### 测试覆盖

1. **组件初始化** - 验证所有组件正常创建
2. **申请页面访问** - 测试UNR申请系统连接
3. **申请步骤结构** - 验证10个步骤完整性
4. **工具方法功能** - 测试表单填写方法
5. **数据验证** - 检查学生数据完整性
6. **申请流程预览** - 完整流程干运行

## 数据映射

### 表单字段映射

| 申请表单字段 | 学生数据字段 | 默认值 |
|-------------|-------------|--------|
| Email | studentData.email | - |
| First Name | studentData.firstName | - |
| Last Name | studentData.lastName | - |
| Birth Date | studentData.birthDate | - |
| Gender | studentData.gender | - |
| SSN | studentData.ssn | - |
| Phone | studentData.phone | - |
| Street Address | studentData.address.street | - |
| City | studentData.address.city | - |
| State | studentData.address.state | - |
| Zip Code | studentData.address.zipCode | - |
| High School | studentData.highSchool.schoolName | 'Maplewood High School' |
| Emergency Contact | studentData.emergencyContact.firstName | 'John' |

### 默认值配置

系统为缺失的数据提供合理的默认值：

```javascript
const defaults = {
    applicationType: 'undergraduate',
    entryType: 'first-time',
    term: 'fall-2024',
    citizenship: 'us-citizen',
    ethnicity: 'prefer-not-to-answer',
    college: 'liberal-arts',
    major: 'computer-science',
    degreeType: 'bachelor'
};
```

## 性能优化

### 提升执行速度

```javascript
const config = {
    stepDelay: 1000,        // 减少步骤间延迟
    timeout: 15000,         // 适当的超时时间
    headless: true          // 无头模式更快
};
```

### 资源管理

```javascript
// 及时清理资源
await browser.close();

// 清理临时数据
applicationFlow.completedSteps = [];
```

## 故障排除

### 常见问题

1. **登录失败**
   ```
   ❌ 登录失败: Invalid credentials
   ```
   **解决方案**: 确认账户已注册且密码正确

2. **表单字段未找到**
   ```
   ⚠️ 未找到邮箱字段，跳过
   ```
   **解决方案**: 网站结构可能已变化，需要更新选择器

3. **步骤执行超时**
   ```
   ❌ 步骤3失败: TimeoutError
   ```
   **解决方案**: 增加超时时间或检查网络连接

### 调试技巧

1. **查看步骤截图**
   ```bash
   # 截图保存在 screenshots/ 目录
   ls screenshots/step*
   ```

2. **启用详细日志**
   ```javascript
   console.log(`当前步骤: ${applicationFlow.currentStep}`);
   console.log(`已完成步骤: ${applicationFlow.completedSteps}`);
   ```

3. **单步调试**
   ```javascript
   // 在特定步骤后暂停
   await browser.sleep(10000);
   ```

## 最佳实践

### 数据准备

1. **确保数据完整性**
   ```javascript
   // 验证必填字段
   const required = ['email', 'firstName', 'lastName'];
   const missing = required.filter(field => !studentData[field]);
   if (missing.length > 0) {
       throw new Error(`缺少必填字段: ${missing.join(', ')}`);
   }
   ```

2. **使用标准化格式**
   ```javascript
   // 日期格式: YYYY-MM-DD
   birthDate: '1995-05-15'
   
   // 电话格式: (XXX) XXX-XXXX
   phone: '(*************'
   ```

### 错误处理

```javascript
try {
    const result = await applicationFlow.execute(studentData);
    console.log('申请成功:', result);
} catch (error) {
    console.error('申请失败:', error.message);
    
    // 查看已完成的步骤
    console.log('已完成步骤:', applicationFlow.completedSteps);
    
    // 从失败点重新开始
    await retryFromStep(applicationFlow.currentStep);
}
```

### 监控和日志

```javascript
// 记录申请进度
applicationFlow.steps.forEach((step, index) => {
    const status = applicationFlow.completedSteps.includes(step.id) ? '✅' : '⏳';
    console.log(`${status} 步骤${step.id}: ${step.name}`);
});
```

## 扩展功能

### 自定义步骤处理

```javascript
class CustomApplicationFlow extends ApplicationFlow {
    async handleCustomStep(studentData) {
        // 自定义步骤处理逻辑
        console.log('执行自定义步骤...');
    }
}
```

### 批量申请

```javascript
const students = await excelReader.readAllStudents();
for (const student of students) {
    await applicationFlow.execute(student);
    await delay(10000); // 避免过于频繁
}
```

## 相关文档

- `registration-guide.md` - 注册流程指南
- `browser-automation.md` - 浏览器控制指南
- `pincode-usage.md` - PIN码获取指南
