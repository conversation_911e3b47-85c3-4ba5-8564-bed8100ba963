{"name": "unr-auto-registration", "version": "1.0.0", "description": "Automated registration and application system for University of Nevada, Reno using Puppeteer", "main": "src/main.js", "type": "module", "scripts": {"start": "node src/main.js", "dev": "node --inspect src/main.js", "test": "node src/test.js", "test:email": "node src/test-email.js", "test:pin": "node src/test-pincode.js", "test:browser": "node src/test-browser.js", "test:registration": "node src/test-registration.js", "test:application": "node src/test-application.js", "test:integration": "node src/test-flow-integration.js", "test:logging": "node src/test-logging.js", "test:unr": "node src/test-unr-scripts.js", "logs:status": "node src/tools/logManager.js status", "logs:analyze": "node src/tools/logManager.js analyze", "logs:quick": "node src/tools/logManager.js quick", "logs:clean": "node src/tools/logManager.js clean", "logs:export": "node src/tools/logManager.js export", "logs:tail": "node src/tools/logManager.js tail", "demo:pin": "node -e \"import('./src/main.js').then(m => new m.UNRAutoRegistration().demonstratePinCodeFlow())\"", "demo:logging": "node src/demo-logging.js", "install-deps": "npm install"}, "keywords": ["puppeteer", "automation", "university", "registration", "form-filling", "email-integration"], "author": "Auto Registration Bot", "license": "MIT", "dependencies": {"puppeteer": "^24.11.1", "xlsx": "^0.18.5", "node-imap": "^0.9.6", "mailparser": "^3.7.1", "winston": "^3.15.0", "dotenv": "^16.4.7", "fs-extra": "^11.2.0", "moment": "^2.30.1"}, "devDependencies": {"nodemon": "^3.1.9"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "local"}}