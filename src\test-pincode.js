/**
 * PIN码获取功能测试脚本
 * 
 * 演示如何从Excel读取邮箱并获取对应的PIN码
 * 
 * <AUTHOR> Registration Bot
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { ExcelReader } from './utils/excelReader.js';
import { PinCodeManager } from './utils/pinCodeManager.js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 获取项目根目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

/**
 * 测试从Excel读取邮箱
 */
async function testExcelEmailReading() {
    console.log('🧪 测试1: 从Excel读取邮箱地址');
    console.log('================================');
    
    try {
        const excelPath = join(projectRoot, 'test.xlsx');
        const reader = new ExcelReader(excelPath);
        
        const studentData = await reader.readStudentData();
        
        console.log('✅ Excel读取成功');
        console.log('📧 学生邮箱:', studentData.email);
        console.log('👤 学生姓名:', `${studentData.firstName} ${studentData.lastName}`);
        
        return studentData;
        
    } catch (error) {
        console.error('❌ Excel读取失败:', error.message);
        return null;
    }
}

/**
 * 测试PIN码管理器创建
 */
async function testPinCodeManagerCreation() {
    console.log('\n🧪 测试2: PIN码管理器创建');
    console.log('================================');
    
    try {
        const pinCodeManager = new PinCodeManager({
            defaultPassword: process.env.EMAIL_PASSWORD || 'defaultpassword123'
        });
        
        console.log('✅ PIN码管理器创建成功');
        console.log('📊 初始状态:', pinCodeManager.getStatus());
        
        return pinCodeManager;
        
    } catch (error) {
        console.error('❌ PIN码管理器创建失败:', error.message);
        return null;
    }
}

/**
 * 测试邮箱连接
 */
async function testEmailConnectionForStudent(pinCodeManager, studentData) {
    console.log('\n🧪 测试3: 学生邮箱连接测试');
    console.log('================================');
    
    try {
        const email = studentData.email;
        console.log(`📧 测试邮箱: ${email}`);
        
        // 这里需要根据实际情况提供邮箱密码
        // 在实际应用中，可能需要用户输入或从配置文件读取
        const emailPassword = process.env.STUDENT_EMAIL_PASSWORD || null;
        
        if (!emailPassword) {
            console.log('⚠️ 未配置学生邮箱密码，跳过连接测试');
            console.log('💡 提示：在.env文件中设置STUDENT_EMAIL_PASSWORD来测试连接');
            return false;
        }
        
        const connectionResult = await pinCodeManager.testEmailConnection(email, emailPassword);
        
        if (connectionResult) {
            console.log('✅ 学生邮箱连接测试成功');
        } else {
            console.log('❌ 学生邮箱连接测试失败');
        }
        
        return connectionResult;
        
    } catch (error) {
        console.error('❌ 邮箱连接测试异常:', error.message);
        return false;
    }
}

/**
 * 演示PIN码获取流程（模拟）
 */
async function demonstratePinCodeFlow(pinCodeManager, studentData) {
    console.log('\n🧪 测试4: PIN码获取流程演示');
    console.log('================================');
    
    try {
        const email = studentData.email;
        console.log(`📧 目标邮箱: ${email}`);
        
        console.log('\n📝 PIN码获取流程说明:');
        console.log('1. 用户在UNR网站注册账户');
        console.log('2. UNR发送验证邮件到用户邮箱');
        console.log('3. 系统自动监听邮箱，获取验证邮件');
        console.log('4. 从邮件中提取9位PIN码');
        console.log('5. 使用PIN码完成账户验证');
        
        console.log('\n⏳ 模拟等待验证邮件...');
        console.log('💡 在实际使用中，这里会真正等待UNR的验证邮件');
        
        // 模拟PIN码（实际使用中会从邮件获取）
        const mockPinCode = '886758143';
        console.log(`🔑 模拟获取到PIN码: ${mockPinCode}`);
        
        // 存储到管理器中
        pinCodeManager.pinCodes.set(email, {
            pinCode: mockPinCode,
            timestamp: new Date(),
            email: email,
            source: 'mock' // 标记为模拟数据
        });
        
        console.log('✅ PIN码获取流程演示完成');
        
        return mockPinCode;
        
    } catch (error) {
        console.error('❌ PIN码获取演示失败:', error.message);
        return null;
    }
}

/**
 * 测试PIN码管理功能
 */
async function testPinCodeManagement(pinCodeManager, studentData) {
    console.log('\n🧪 测试5: PIN码管理功能');
    console.log('================================');
    
    try {
        const email = studentData.email;
        
        // 测试获取存储的PIN码
        console.log('📋 获取已存储的PIN码...');
        const storedPin = pinCodeManager.getStoredPinCode(email);
        
        if (storedPin) {
            console.log('✅ 找到已存储的PIN码:', storedPin.pinCode);
            console.log('⏰ 存储时间:', storedPin.timestamp);
        } else {
            console.log('⚠️ 未找到已存储的PIN码');
        }
        
        // 测试获取所有PIN码
        console.log('\n📊 所有已存储的PIN码:');
        const allPinCodes = pinCodeManager.getAllStoredPinCodes();
        console.log(allPinCodes);
        
        // 测试管理器状态
        console.log('\n📈 PIN码管理器状态:');
        const status = pinCodeManager.getStatus();
        console.log(status);
        
        console.log('✅ PIN码管理功能测试完成');
        
        return true;
        
    } catch (error) {
        console.error('❌ PIN码管理测试失败:', error.message);
        return false;
    }
}

/**
 * 运行完整的PIN码测试套件
 */
async function runPinCodeTests() {
    console.log('🚀 PIN码获取功能测试套件');
    console.log('=====================================\n');
    
    try {
        // 1. 测试Excel读取
        const studentData = await testExcelEmailReading();
        if (!studentData) {
            console.log('❌ Excel读取失败，无法继续测试');
            return false;
        }
        
        // 2. 测试PIN码管理器创建
        const pinCodeManager = await testPinCodeManagerCreation();
        if (!pinCodeManager) {
            console.log('❌ PIN码管理器创建失败，无法继续测试');
            return false;
        }
        
        // 3. 测试邮箱连接
        await testEmailConnectionForStudent(pinCodeManager, studentData);
        
        // 4. 演示PIN码获取流程
        await demonstratePinCodeFlow(pinCodeManager, studentData);
        
        // 5. 测试PIN码管理功能
        await testPinCodeManagement(pinCodeManager, studentData);
        
        console.log('\n🎉 PIN码测试套件执行完成！');
        console.log('\n💡 使用说明:');
        console.log('1. 确保test.xlsx文件包含有效的学生邮箱');
        console.log('2. 在实际使用中，需要先在UNR网站注册账户');
        console.log('3. 注册后系统会自动等待验证邮件并提取PIN码');
        console.log('4. 提取的PIN码将用于完成账户验证流程');
        
        return true;
        
    } catch (error) {
        console.error('❌ PIN码测试套件执行失败:', error.message);
        return false;
    }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
    runPinCodeTests().catch(console.error);
}

export { runPinCodeTests };
