/**
 * UNR申请步骤处理器
 * 
 * 功能：
 * 1. 处理UNR申请的10个具体步骤
 * 2. 智能识别当前步骤状态
 * 3. 自适应步骤导航
 * 4. 处理步骤间的数据传递
 * 
 * <AUTHOR> Registration Bot
 */

import { UNRFormFiller } from './formFiller.js';

/**
 * UNR申请步骤处理器类
 */
export class UNRStepProcessor {
    constructor(browserController, config = {}) {
        this.browser = browserController;
        this.logger = config.logger || null;
        this.formFiller = new UNRFormFiller(browserController, config);
        
        this.config = {
            stepTimeout: config.stepTimeout || 30000,
            navigationDelay: config.navigationDelay || 2000,
            retryCount: config.retryCount || 3,
            ...config
        };
        
        // UNR申请步骤定义
        this.unrSteps = [
            {
                id: 1,
                name: 'Application Information',
                handler: 'processApplicationInformation',
                expectedElements: ['.application-type', '#applicant-type'],
                navigationSelectors: ['button:contains("Continue")', '#continue-btn']
            },
            {
                id: 2,
                name: 'Nondegree Application Instructions',
                handler: 'processNondegreeInstructions',
                expectedElements: ['.instructions', '.agreement-checkbox'],
                navigationSelectors: ['button:contains("Continue")', '#continue-btn']
            },
            {
                id: 3,
                name: 'Application Term',
                handler: 'processApplicationTerm',
                expectedElements: ['select[name="term"]', '.term-selection'],
                navigationSelectors: ['button:contains("Continue")', '#continue-btn']
            },
            {
                id: 4,
                name: 'Personal Background',
                handler: 'processPersonalBackground',
                expectedElements: ['#birthDate', '#gender', '#ssn'],
                navigationSelectors: ['button:contains("Continue")', '#continue-btn']
            },
            {
                id: 5,
                name: 'Background Continued',
                handler: 'processBackgroundContinued',
                expectedElements: ['#phone', '#citizenship', '#ethnicity'],
                navigationSelectors: ['button:contains("Continue")', '#continue-btn']
            },
            {
                id: 6,
                name: 'Emergency Contact',
                handler: 'processEmergencyContact',
                expectedElements: ['#emergencyFirstName', '#emergencyLastName'],
                navigationSelectors: ['button:contains("Continue")', '#continue-btn']
            },
            {
                id: 7,
                name: 'Academic History',
                handler: 'processAcademicHistory',
                expectedElements: ['#highSchoolName', '#graduationDate'],
                navigationSelectors: ['button:contains("Continue")', '#continue-btn']
            },
            {
                id: 8,
                name: 'Program Selection',
                handler: 'processProgramSelection',
                expectedElements: ['#college', '#major', '#degreeType'],
                navigationSelectors: ['button:contains("Continue")', '#continue-btn']
            },
            {
                id: 9,
                name: 'Signature',
                handler: 'processSignature',
                expectedElements: ['#signature', '#signatureDate'],
                navigationSelectors: ['button:contains("Continue")', '#continue-btn']
            },
            {
                id: 10,
                name: 'Review',
                handler: 'processReview',
                expectedElements: ['.review-section', '.application-summary'],
                navigationSelectors: ['button:contains("Submit")', '#submit-btn']
            }
        ];
        
        this.currentStepId = 0;
        this.completedSteps = [];
        this.stepData = {};
    }

    /**
     * 处理完整的申请流程
     */
    async processAllSteps(studentData) {
        try {
            this.log('info', 'Starting UNR application step processing', {
                totalSteps: this.unrSteps.length,
                studentEmail: studentData.email
            });
            
            for (const step of this.unrSteps) {
                await this.processStep(step, studentData);
            }
            
            this.log('info', 'All application steps completed successfully', {
                completedSteps: this.completedSteps.length,
                totalSteps: this.unrSteps.length
            });
            
            return {
                success: true,
                completedSteps: this.completedSteps,
                totalSteps: this.unrSteps.length
            };
            
        } catch (error) {
            this.log('error', 'Application step processing failed', {
                error: error.message,
                currentStep: this.currentStepId,
                completedSteps: this.completedSteps.length
            });
            throw error;
        }
    }

    /**
     * 处理单个步骤
     */
    async processStep(step, studentData) {
        try {
            this.currentStepId = step.id;
            
            this.log('info', `Processing step ${step.id}: ${step.name}`);
            
            // 等待步骤页面加载
            await this.waitForStep(step);
            
            // 截图记录
            await this.browser.takeScreenshot(`step-${step.id}-start`);
            
            // 执行步骤处理逻辑
            await this[step.handler](studentData);
            
            // 导航到下一步
            await this.navigateToNextStep(step);
            
            // 记录步骤完成
            this.completedSteps.push(step.id);
            
            this.log('info', `Step ${step.id} completed: ${step.name}`);
            
            // 截图记录
            await this.browser.takeScreenshot(`step-${step.id}-complete`);
            
        } catch (error) {
            await this.browser.takeScreenshot(`step-${step.id}-error`);
            this.log('error', `Step ${step.id} failed: ${step.name}`, {
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 等待步骤页面加载
     */
    async waitForStep(step) {
        this.log('debug', `Waiting for step ${step.id} elements`);
        
        // 尝试等待步骤特有的元素
        for (const selector of step.expectedElements) {
            try {
                await this.browser.waitForElement(selector, { timeout: 5000 });
                this.log('debug', `Found step element: ${selector}`);
                return;
            } catch (error) {
                continue;
            }
        }
        
        // 如果没有找到特定元素，等待页面稳定
        await this.browser.waitForLoadState('domcontentloaded');
        await this.browser.sleep(2000);
    }

    /**
     * 步骤1: Application Information
     */
    async processApplicationInformation(studentData) {
        this.log('info', 'Processing Application Information step');
        
        // 选择申请类型
        const applicationTypeSelectors = [
            'select[name="applicationType"]',
            '#application-type',
            'input[name="applicationType"][value="undergraduate"]'
        ];
        
        for (const selector of applicationTypeSelectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                
                if (selector.includes('select')) {
                    await this.browser.selectOption(selector, 'undergraduate');
                } else if (selector.includes('input')) {
                    await this.browser.clickElement(selector);
                }
                
                this.log('info', 'Application type selected: undergraduate');
                break;
            } catch (error) {
                continue;
            }
        }
        
        // 确认个人信息
        await this.confirmPersonalInformation(studentData);
    }

    /**
     * 步骤2: Nondegree Application Instructions
     */
    async processNondegreeInstructions(studentData) {
        this.log('info', 'Processing Nondegree Application Instructions step');
        
        // 查找并勾选确认复选框
        const confirmSelectors = [
            'input[type="checkbox"][name*="confirm"]',
            'input[type="checkbox"][name*="agree"]',
            'input[type="checkbox"][name*="understand"]',
            '.agreement-checkbox input'
        ];
        
        for (const selector of confirmSelectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.clickElement(selector);
                this.log('info', 'Instructions confirmation checkbox checked');
                break;
            } catch (error) {
                continue;
            }
        }
    }

    /**
     * 步骤3: Application Term
     */
    async processApplicationTerm(studentData) {
        this.log('info', 'Processing Application Term step');
        
        // 选择申请学期
        const termSelectors = [
            'select[name="term"]',
            'select[name="applicationTerm"]',
            '#term-select'
        ];
        
        const defaultTerm = 'Fall 2024'; // 可以从配置中获取
        
        for (const selector of termSelectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.selectOption(selector, defaultTerm);
                this.log('info', `Application term selected: ${defaultTerm}`);
                break;
            } catch (error) {
                continue;
            }
        }
        
        // 选择入学类型
        const entryTypeSelectors = [
            'select[name="entryType"]',
            'select[name="admissionType"]',
            '#entry-type'
        ];
        
        for (const selector of entryTypeSelectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.selectOption(selector, 'First-time freshman');
                this.log('info', 'Entry type selected: First-time freshman');
                break;
            } catch (error) {
                continue;
            }
        }
    }

    /**
     * 步骤4: Personal Background
     */
    async processPersonalBackground(studentData) {
        this.log('info', 'Processing Personal Background step');
        
        // 使用表单填写器填写个人信息
        await this.formFiller.fillPersonalInfoForm(studentData);
        
        // 填写地址信息
        await this.formFiller.fillAddressForm(studentData);
    }

    /**
     * 步骤5: Background Continued
     */
    async processBackgroundContinued(studentData) {
        this.log('info', 'Processing Background Continued step');
        
        // 选择公民身份
        const citizenshipSelectors = [
            'select[name="citizenship"]',
            '#citizenship'
        ];
        
        for (const selector of citizenshipSelectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.selectOption(selector, 'US Citizen');
                this.log('info', 'Citizenship selected: US Citizen');
                break;
            } catch (error) {
                continue;
            }
        }
        
        // 选择种族/民族（可选）
        const ethnicitySelectors = [
            'select[name="ethnicity"]',
            '#ethnicity'
        ];
        
        for (const selector of ethnicitySelectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.selectOption(selector, 'Prefer not to answer');
                this.log('info', 'Ethnicity selected: Prefer not to answer');
                break;
            } catch (error) {
                continue;
            }
        }
    }

    /**
     * 步骤6: Emergency Contact
     */
    async processEmergencyContact(studentData) {
        this.log('info', 'Processing Emergency Contact step');
        
        const emergencyContact = studentData.emergencyContact || {
            firstName: 'John',
            lastName: 'Smith',
            relationship: 'Other',
            email: '<EMAIL>',
            language: 'English'
        };
        
        // 填写紧急联系人信息
        const contactFields = {
            firstName: ['#emergencyFirstName', 'input[name="emergencyFirstName"]'],
            lastName: ['#emergencyLastName', 'input[name="emergencyLastName"]'],
            email: ['#emergencyEmail', 'input[name="emergencyEmail"]']
        };
        
        for (const [field, selectors] of Object.entries(contactFields)) {
            await this.formFiller.fillField(selectors, emergencyContact[field], `Emergency Contact ${field}`);
        }
        
        // 选择关系
        const relationshipSelectors = [
            'select[name="emergencyRelationship"]',
            '#emergency-relationship'
        ];
        
        for (const selector of relationshipSelectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.selectOption(selector, emergencyContact.relationship);
                this.log('info', `Emergency contact relationship selected: ${emergencyContact.relationship}`);
                break;
            } catch (error) {
                continue;
            }
        }
    }

    /**
     * 步骤7: Academic History
     */
    async processAcademicHistory(studentData) {
        this.log('info', 'Processing Academic History step');
        
        // 使用表单填写器填写学术历史
        await this.formFiller.fillAcademicHistoryForm(studentData);
    }

    /**
     * 步骤8: Program Selection
     */
    async processProgramSelection(studentData) {
        this.log('info', 'Processing Program Selection step');
        
        // 选择学院
        const collegeSelectors = [
            'select[name="college"]',
            '#college'
        ];
        
        for (const selector of collegeSelectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.selectOption(selector, 'College of Liberal Arts');
                this.log('info', 'College selected: College of Liberal Arts');
                break;
            } catch (error) {
                continue;
            }
        }
        
        // 选择专业
        const majorSelectors = [
            'select[name="major"]',
            '#major'
        ];
        
        for (const selector of majorSelectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.selectOption(selector, 'Computer Science');
                this.log('info', 'Major selected: Computer Science');
                break;
            } catch (error) {
                continue;
            }
        }
    }

    /**
     * 步骤9: Signature
     */
    async processSignature(studentData) {
        this.log('info', 'Processing Signature step');
        
        // 电子签名
        const signatureSelectors = [
            '#signature',
            'input[name="signature"]'
        ];
        
        const fullName = `${studentData.firstName} ${studentData.lastName}`;
        await this.formFiller.fillField(signatureSelectors, fullName, 'Electronic Signature');
        
        // 签名日期
        const dateSelectors = [
            '#signatureDate',
            'input[name="signatureDate"]'
        ];
        
        const today = new Date().toISOString().split('T')[0];
        await this.formFiller.fillField(dateSelectors, today, 'Signature Date');
        
        // 同意条款
        const agreeSelectors = [
            'input[type="checkbox"][name*="agree"]',
            '#agree-terms'
        ];
        
        for (const selector of agreeSelectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.clickElement(selector);
                this.log('info', 'Terms agreement checkbox checked');
                break;
            } catch (error) {
                continue;
            }
        }
    }

    /**
     * 步骤10: Review
     */
    async processReview(studentData) {
        this.log('info', 'Processing Review step');
        
        // 等待审核页面加载
        await this.browser.waitForLoadState('domcontentloaded');
        
        // 检查信息是否正确
        this.log('info', 'Reviewing application information');
        
        // 这里可以添加信息验证逻辑
        await this.browser.sleep(3000);
        
        this.log('info', 'Application review completed, ready for submission');
    }

    /**
     * 确认个人信息
     */
    async confirmPersonalInformation(studentData) {
        // 检查并确认个人信息是否正确显示
        this.log('info', 'Confirming personal information display');
        
        // 这里可以添加信息确认逻辑
        await this.browser.sleep(1000);
    }

    /**
     * 导航到下一步
     */
    async navigateToNextStep(step) {
        this.log('debug', `Navigating from step ${step.id} to next step`);
        
        for (const selector of step.navigationSelectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 5000 });
                await this.browser.clickElement(selector);
                
                this.log('info', `Navigation successful using selector: ${selector}`);
                
                // 等待页面跳转
                await this.browser.sleep(this.config.navigationDelay);
                return;
                
            } catch (error) {
                this.log('debug', `Navigation failed with selector: ${selector}`, {
                    error: error.message
                });
                continue;
            }
        }
        
        this.log('warn', `Failed to navigate from step ${step.id} with all selectors`);
    }

    /**
     * 获取当前步骤信息
     */
    async getCurrentStepInfo() {
        try {
            const stepInfo = await this.browser.page.evaluate(() => {
                // 查找步骤指示器
                const indicators = document.querySelectorAll('.step-indicator, .progress-step, [class*="step"]');
                const currentStep = document.querySelector('.step.active, .step.current, .step-indicator.active');
                
                return {
                    totalSteps: indicators.length,
                    currentStepText: currentStep?.textContent?.trim() || '',
                    currentStepIndex: Array.from(indicators).indexOf(currentStep) + 1
                };
            });
            
            return stepInfo;
            
        } catch (error) {
            this.log('warn', 'Failed to get current step info', { error: error.message });
            return null;
        }
    }

    /**
     * 日志记录辅助方法
     */
    log(level, message, meta = {}) {
        if (this.logger) {
            this.logger.log(level, message, { ...meta, component: 'UNRStepProcessor' });
        } else {
            console.log(`[${level.toUpperCase()}] ${message}`, meta);
        }
    }
}
