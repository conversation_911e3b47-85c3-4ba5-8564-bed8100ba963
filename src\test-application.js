/**
 * 申请流程测试脚本
 * 
 * 测试UNR申请表单自动化功能（10个步骤）
 * 
 * <AUTHOR> Registration Bot
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { ExcelReader } from './utils/excelReader.js';
import { BrowserController } from './automation/browserController.js';
import { ApplicationFlow } from './automation/applicationFlow.js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 获取项目根目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

/**
 * 测试申请流程组件初始化
 */
async function testApplicationComponents() {
    console.log('🧪 测试1: 申请流程组件初始化');
    console.log('================================');
    
    let browser = null;
    
    try {
        // 1. 测试Excel读取
        console.log('📊 测试Excel数据读取...');
        const excelPath = join(projectRoot, 'test.xlsx');
        const reader = new ExcelReader(excelPath);
        const studentData = await reader.readStudentData();
        
        console.log('✅ Excel数据读取成功');
        console.log(`👤 学生: ${studentData.firstName} ${studentData.lastName}`);
        
        // 2. 测试浏览器控制器
        console.log('\n🌐 测试浏览器控制器...');
        browser = new BrowserController({
            headless: true,
            timeout: 15000
        });
        
        await browser.launch();
        console.log('✅ 浏览器启动成功');
        
        // 3. 测试申请流程控制器
        console.log('\n📋 测试申请流程控制器...');
        const applicationFlow = new ApplicationFlow(browser, {
            applyUrl: 'https://admissions.unr.edu/apply/'
        });
        
        console.log('✅ 申请流程控制器创建成功');
        console.log(`📝 申请步骤总数: ${applicationFlow.totalSteps}`);
        
        // 显示所有申请步骤
        console.log('\n📋 申请流程步骤:');
        applicationFlow.steps.forEach(step => {
            console.log(`  ${step.id}. ${step.name}`);
        });
        
        return { studentData, browser, applicationFlow };
        
    } catch (error) {
        console.error('❌ 组件初始化测试失败:', error.message);
        if (browser) {
            await browser.close();
        }
        return null;
    }
}

/**
 * 测试申请页面访问
 */
async function testApplicationPageAccess(browser) {
    console.log('\n🧪 测试2: 申请页面访问');
    console.log('================================');
    
    try {
        const applyUrl = 'https://admissions.unr.edu/apply/';
        
        console.log(`🔗 访问申请页面: ${applyUrl}`);
        await browser.navigateTo(applyUrl, { screenshot: false });
        
        const title = await browser.getPageTitle();
        const url = await browser.getCurrentUrl();
        
        console.log(`📄 页面标题: ${title}`);
        console.log(`🔗 当前URL: ${url}`);
        
        if (url.includes('unr.edu')) {
            console.log('✅ 成功访问UNR申请页面');
            
            // 检查是否需要登录
            try {
                await browser.waitForElement('input[type="email"], input[name="email"]', { timeout: 5000 });
                console.log('🔐 检测到登录页面');
                return 'login_required';
            } catch (error) {
                console.log('📋 检测到申请表单页面');
                return 'application_form';
            }
        } else {
            console.log('❌ 未能访问到UNR申请页面');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 申请页面访问测试失败:', error.message);
        return false;
    }
}

/**
 * 测试申请步骤结构
 */
async function testApplicationStepStructure(applicationFlow) {
    console.log('\n🧪 测试3: 申请步骤结构验证');
    console.log('================================');
    
    try {
        console.log('🔍 验证申请步骤结构...');
        
        const expectedSteps = [
            'Application Information',
            'Nondegree Application Instructions', 
            'Application Term',
            'Personal Background',
            'Background Continued',
            'Emergency Contact',
            'Academic History',
            'Program Selection',
            'Signature',
            'Review'
        ];
        
        console.log(`📊 预期步骤数: ${expectedSteps.length}`);
        console.log(`📊 实际步骤数: ${applicationFlow.steps.length}`);
        
        if (applicationFlow.steps.length !== expectedSteps.length) {
            console.log('❌ 步骤数量不匹配');
            return false;
        }
        
        let allStepsMatch = true;
        for (let i = 0; i < expectedSteps.length; i++) {
            const expected = expectedSteps[i];
            const actual = applicationFlow.steps[i].name;
            
            if (expected === actual) {
                console.log(`✅ 步骤${i + 1}: ${actual}`);
            } else {
                console.log(`❌ 步骤${i + 1}: 期望"${expected}"，实际"${actual}"`);
                allStepsMatch = false;
            }
        }
        
        if (allStepsMatch) {
            console.log('✅ 所有申请步骤结构验证通过');
            return true;
        } else {
            console.log('❌ 申请步骤结构验证失败');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 申请步骤结构测试失败:', error.message);
        return false;
    }
}

/**
 * 测试工具方法功能
 */
async function testUtilityMethods(browser, applicationFlow, studentData) {
    console.log('\n🧪 测试4: 工具方法功能');
    console.log('================================');
    
    try {
        console.log('🔧 测试工具方法...');
        
        // 导航到测试页面
        await browser.navigateTo('https://www.google.com', { screenshot: false });
        
        // 测试fillFieldWithSelectors方法
        console.log('✏️ 测试字段填写方法...');
        const searchSelectors = ['textarea[name="q"]', 'input[name="q"]'];
        await applicationFlow.fillFieldWithSelectors(searchSelectors, 'test', '搜索框');
        
        // 测试clickWithSelectors方法
        console.log('👆 测试点击方法...');
        const buttonSelectors = ['input[type="submit"]', 'button[type="submit"]'];
        await applicationFlow.clickWithSelectors(buttonSelectors, '搜索按钮');
        
        console.log('✅ 工具方法测试完成');
        return true;
        
    } catch (error) {
        console.error('❌ 工具方法测试失败:', error.message);
        return false;
    }
}

/**
 * 测试数据验证
 */
async function testDataValidation(studentData) {
    console.log('\n🧪 测试5: 学生数据验证');
    console.log('================================');
    
    try {
        console.log('📊 验证学生数据完整性...');
        
        const requiredFields = [
            'email', 'firstName', 'lastName'
        ];
        
        const optionalFields = [
            'birthDate', 'gender', 'ssn', 'phone', 'address', 'highSchool', 'emergencyContact'
        ];
        
        // 检查必填字段
        const missingRequired = requiredFields.filter(field => !studentData[field]);
        if (missingRequired.length > 0) {
            console.log(`❌ 缺少必填字段: ${missingRequired.join(', ')}`);
            return false;
        }
        
        console.log('✅ 所有必填字段完整');
        
        // 检查可选字段
        const presentOptional = optionalFields.filter(field => studentData[field]);
        console.log(`📋 可选字段: ${presentOptional.length}/${optionalFields.length} 个已填写`);
        
        presentOptional.forEach(field => {
            console.log(`  ✅ ${field}: 已提供`);
        });
        
        const missingOptional = optionalFields.filter(field => !studentData[field]);
        if (missingOptional.length > 0) {
            console.log(`⚠️ 缺少可选字段: ${missingOptional.join(', ')}`);
            console.log('💡 系统将使用默认值填充');
        }
        
        console.log('✅ 学生数据验证完成');
        return true;
        
    } catch (error) {
        console.error('❌ 数据验证测试失败:', error.message);
        return false;
    }
}

/**
 * 测试申请流程预览（干运行）
 */
async function testApplicationFlowPreview(applicationFlow, studentData) {
    console.log('\n🧪 测试6: 申请流程预览');
    console.log('================================');
    
    try {
        console.log('🔄 申请流程预览（干运行模式）...');
        console.log('💡 注意: 这是预览模式，不会实际提交申请');
        
        console.log('\n📋 完整申请流程预览:');
        
        applicationFlow.steps.forEach((step, index) => {
            const status = '⏳'; // 所有步骤都是待执行状态
            console.log(`${status} 步骤${step.id}: ${step.name}`);
            
            // 显示每个步骤的主要操作
            switch (step.id) {
                case 1:
                    console.log('    - 选择申请类型');
                    console.log('    - 确认个人信息');
                    break;
                case 2:
                    console.log('    - 阅读申请说明');
                    console.log('    - 确认理解条款');
                    break;
                case 3:
                    console.log('    - 选择申请学期');
                    console.log('    - 选择入学类型');
                    break;
                case 4:
                    console.log('    - 填写生日、性别、SSN');
                    console.log('    - 填写地址信息');
                    break;
                case 5:
                    console.log('    - 填写电话号码');
                    console.log('    - 选择公民身份和种族');
                    break;
                case 6:
                    console.log('    - 填写紧急联系人信息');
                    console.log('    - 选择联系人关系和语言');
                    break;
                case 7:
                    console.log('    - 填写高中信息');
                    console.log('    - 选择就读时间和毕业状态');
                    break;
                case 8:
                    console.log('    - 选择申请学院和专业');
                    console.log('    - 选择学位类型');
                    break;
                case 9:
                    console.log('    - 电子签名');
                    console.log('    - 同意申请条款');
                    break;
                case 10:
                    console.log('    - 审核所有信息');
                    console.log('    - 最终提交申请');
                    break;
            }
        });
        
        console.log('\n💡 申请流程说明:');
        console.log('- 系统会自动填写学生信息到对应表单');
        console.log('- 每个步骤完成后会自动进入下一步');
        console.log('- 所有步骤都有错误处理和重试机制');
        console.log('- 关键步骤会自动截图保存');
        
        console.log('\n📊 数据使用情况:');
        console.log(`- 学生姓名: ${studentData.firstName} ${studentData.lastName}`);
        console.log(`- 邮箱地址: ${studentData.email}`);
        console.log(`- 高中信息: ${studentData.highSchool?.schoolName || '使用默认值'}`);
        console.log(`- 紧急联系人: ${studentData.emergencyContact?.firstName || '使用默认值'}`);
        
        return true;
        
    } catch (error) {
        console.error('❌ 申请流程预览失败:', error.message);
        return false;
    }
}

/**
 * 运行申请流程测试套件
 */
async function runApplicationTests() {
    console.log('🚀 UNR申请流程测试套件');
    console.log('=====================================\n');
    
    let browser = null;
    
    try {
        // 1. 测试组件初始化
        const components = await testApplicationComponents();
        if (!components) {
            console.log('❌ 组件初始化失败，无法继续测试');
            return false;
        }
        
        const { studentData, browser: browserInstance, applicationFlow } = components;
        browser = browserInstance;
        
        // 2. 测试申请页面访问
        const pageAccessResult = await testApplicationPageAccess(browser);
        
        // 3. 测试申请步骤结构
        const stepStructureResult = await testApplicationStepStructure(applicationFlow);
        
        // 4. 测试工具方法
        const utilityMethodsResult = await testUtilityMethods(browser, applicationFlow, studentData);
        
        // 5. 测试数据验证
        const dataValidationResult = await testDataValidation(studentData);
        
        // 6. 测试申请流程预览
        const flowPreviewResult = await testApplicationFlowPreview(applicationFlow, studentData);
        
        // 输出测试总结
        console.log('\n📊 申请流程测试结果总结:');
        console.log('=====================================');
        
        const results = [
            { name: '组件初始化', success: true },
            { name: '申请页面访问', success: pageAccessResult !== false },
            { name: '申请步骤结构', success: stepStructureResult },
            { name: '工具方法功能', success: utilityMethodsResult },
            { name: '数据验证', success: dataValidationResult },
            { name: '申请流程预览', success: flowPreviewResult }
        ];
        
        results.forEach(result => {
            const status = result.success ? '✅ 通过' : '❌ 失败';
            console.log(`${result.name}: ${status}`);
        });
        
        const passedTests = results.filter(r => r.success).length;
        const totalTests = results.length;
        
        console.log(`\n总计: ${passedTests}/${totalTests} 个测试通过`);
        
        if (passedTests === totalTests) {
            console.log('🎉 所有申请流程测试通过！');
            console.log('\n💡 下一步:');
            console.log('1. 确保已完成UNR账户注册');
            console.log('2. 准备完整的学生数据');
            console.log('3. 运行完整的申请流程');
            console.log('4. 监控申请状态和结果');
        } else {
            console.log('⚠️ 部分测试失败，请检查配置或网络连接');
        }
        
        return passedTests === totalTests;
        
    } catch (error) {
        console.error('❌ 申请流程测试异常:', error.message);
        return false;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
    runApplicationTests().catch(console.error);
}

export { runApplicationTests };
