/**
 * UNR页面结构分析器
 * 
 * 功能：
 * 1. 分析UNR网站页面结构
 * 2. 识别表单字段和选择器
 * 3. 生成页面映射配置
 * 4. 检测页面变化
 * 
 * <AUTHOR> Registration Bot
 */

/**
 * UNR页面分析器类
 */
export class UNRPageAnalyzer {
    constructor(browserController, config = {}) {
        this.browser = browserController;
        this.logger = config.logger || null;
        this.config = {
            timeout: config.timeout || 10000,
            retryCount: config.retryCount || 3,
            ...config
        };
        
        // UNR特定的页面配置
        this.unrPages = {
            registration: {
                url: 'https://admissions.unr.edu/account/register',
                expectedTitle: 'Create Account',
                requiredElements: ['#email', '#password', '#confirmPassword']
            },
            login: {
                url: 'https://admissions.unr.edu/account/login',
                expectedTitle: 'Sign In',
                requiredElements: ['input[name="email"]', 'input[name="password"]']
            },
            application: {
                url: 'https://admissions.unr.edu/apply/',
                expectedTitle: 'Application',
                requiredElements: ['.application-form', '.step-indicator']
            }
        };
        
        this.fieldMappings = new Map();
        this.pageStructures = new Map();
    }

    /**
     * 分析注册页面结构
     */
    async analyzeRegistrationPage() {
        try {
            this.log('info', 'Starting registration page analysis');
            
            await this.browser.navigateTo(this.unrPages.registration.url);
            await this.browser.waitForLoadState('domcontentloaded');
            
            const pageInfo = await this.getPageInfo();
            this.log('info', 'Registration page loaded', pageInfo);
            
            // 分析表单字段
            const formFields = await this.analyzeFormFields();
            
            // 分析按钮和链接
            const buttons = await this.analyzeButtons();
            
            // 分析验证规则
            const validationRules = await this.analyzeValidationRules();
            
            const structure = {
                pageType: 'registration',
                url: this.unrPages.registration.url,
                title: pageInfo.title,
                formFields,
                buttons,
                validationRules,
                timestamp: new Date().toISOString()
            };
            
            this.pageStructures.set('registration', structure);
            
            this.log('info', 'Registration page analysis completed', {
                fieldsFound: formFields.length,
                buttonsFound: buttons.length
            });
            
            return structure;
            
        } catch (error) {
            this.log('error', 'Registration page analysis failed', { error: error.message });
            throw error;
        }
    }

    /**
     * 分析申请页面结构
     */
    async analyzeApplicationPage() {
        try {
            this.log('info', 'Starting application page analysis');
            
            await this.browser.navigateTo(this.unrPages.application.url);
            await this.browser.waitForLoadState('domcontentloaded');
            
            const pageInfo = await this.getPageInfo();
            this.log('info', 'Application page loaded', pageInfo);
            
            // 分析申请步骤
            const steps = await this.analyzeApplicationSteps();
            
            // 分析当前步骤的表单字段
            const currentStepFields = await this.analyzeCurrentStepFields();
            
            // 分析导航元素
            const navigation = await this.analyzeNavigation();
            
            const structure = {
                pageType: 'application',
                url: this.unrPages.application.url,
                title: pageInfo.title,
                steps,
                currentStepFields,
                navigation,
                timestamp: new Date().toISOString()
            };
            
            this.pageStructures.set('application', structure);
            
            this.log('info', 'Application page analysis completed', {
                stepsFound: steps.length,
                fieldsFound: currentStepFields.length
            });
            
            return structure;
            
        } catch (error) {
            this.log('error', 'Application page analysis failed', { error: error.message });
            throw error;
        }
    }

    /**
     * 获取页面基本信息
     */
    async getPageInfo() {
        const title = await this.browser.getPageTitle();
        const url = await this.browser.getCurrentUrl();
        
        return { title, url };
    }

    /**
     * 分析表单字段
     */
    async analyzeFormFields() {
        const fields = [];
        
        // 常见的表单字段选择器
        const fieldSelectors = [
            'input[type="text"]',
            'input[type="email"]',
            'input[type="password"]',
            'input[type="tel"]',
            'input[type="date"]',
            'select',
            'textarea',
            'input[type="checkbox"]',
            'input[type="radio"]'
        ];
        
        for (const selector of fieldSelectors) {
            try {
                const elements = await this.browser.page.$$eval(selector, elements => {
                    return elements.map(el => ({
                        tagName: el.tagName.toLowerCase(),
                        type: el.type || 'unknown',
                        name: el.name || '',
                        id: el.id || '',
                        placeholder: el.placeholder || '',
                        required: el.required || false,
                        className: el.className || '',
                        value: el.value || '',
                        visible: el.offsetParent !== null
                    }));
                });
                
                fields.push(...elements.map(el => ({
                    ...el,
                    selector,
                    detectedSelectors: this.generateAlternativeSelectors(el)
                })));
                
            } catch (error) {
                // 元素不存在，继续下一个选择器
                continue;
            }
        }
        
        return fields;
    }

    /**
     * 分析按钮元素
     */
    async analyzeButtons() {
        const buttons = [];
        
        const buttonSelectors = [
            'button',
            'input[type="submit"]',
            'input[type="button"]',
            'a[role="button"]',
            '.btn',
            '.button'
        ];
        
        for (const selector of buttonSelectors) {
            try {
                const elements = await this.browser.page.$$eval(selector, elements => {
                    return elements.map(el => ({
                        tagName: el.tagName.toLowerCase(),
                        type: el.type || 'button',
                        text: el.textContent?.trim() || '',
                        value: el.value || '',
                        id: el.id || '',
                        className: el.className || '',
                        disabled: el.disabled || false,
                        visible: el.offsetParent !== null
                    }));
                });
                
                buttons.push(...elements.map(el => ({
                    ...el,
                    selector,
                    detectedSelectors: this.generateAlternativeSelectors(el)
                })));
                
            } catch (error) {
                continue;
            }
        }
        
        return buttons;
    }

    /**
     * 分析验证规则
     */
    async analyzeValidationRules() {
        const rules = [];
        
        try {
            const validationInfo = await this.browser.page.evaluate(() => {
                const inputs = document.querySelectorAll('input, select, textarea');
                return Array.from(inputs).map(input => ({
                    name: input.name || input.id,
                    required: input.required,
                    pattern: input.pattern || '',
                    minLength: input.minLength || 0,
                    maxLength: input.maxLength || 0,
                    min: input.min || '',
                    max: input.max || '',
                    step: input.step || '',
                    validationMessage: input.validationMessage || ''
                }));
            });
            
            rules.push(...validationInfo);
            
        } catch (error) {
            this.log('warn', 'Failed to analyze validation rules', { error: error.message });
        }
        
        return rules;
    }

    /**
     * 分析申请步骤
     */
    async analyzeApplicationSteps() {
        const steps = [];
        
        try {
            const stepInfo = await this.browser.page.evaluate(() => {
                // 查找步骤指示器
                const stepIndicators = document.querySelectorAll('.step, .step-item, .progress-step, [class*="step"]');
                
                return Array.from(stepIndicators).map((step, index) => ({
                    index: index + 1,
                    text: step.textContent?.trim() || '',
                    className: step.className || '',
                    active: step.classList.contains('active') || step.classList.contains('current'),
                    completed: step.classList.contains('completed') || step.classList.contains('done')
                }));
            });
            
            steps.push(...stepInfo);
            
        } catch (error) {
            this.log('warn', 'Failed to analyze application steps', { error: error.message });
        }
        
        return steps;
    }

    /**
     * 分析当前步骤的表单字段
     */
    async analyzeCurrentStepFields() {
        // 重用表单字段分析逻辑，但只关注可见字段
        const allFields = await this.analyzeFormFields();
        return allFields.filter(field => field.visible);
    }

    /**
     * 分析导航元素
     */
    async analyzeNavigation() {
        const navigation = {
            nextButton: null,
            prevButton: null,
            submitButton: null,
            cancelButton: null
        };
        
        try {
            const navInfo = await this.browser.page.evaluate(() => {
                const buttons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
                const nav = {};
                
                Array.from(buttons).forEach(btn => {
                    const text = (btn.textContent || btn.value || '').toLowerCase();
                    
                    if (text.includes('next') || text.includes('continue')) {
                        nav.nextButton = {
                            text: btn.textContent || btn.value,
                            id: btn.id,
                            className: btn.className
                        };
                    } else if (text.includes('prev') || text.includes('back')) {
                        nav.prevButton = {
                            text: btn.textContent || btn.value,
                            id: btn.id,
                            className: btn.className
                        };
                    } else if (text.includes('submit') || text.includes('apply')) {
                        nav.submitButton = {
                            text: btn.textContent || btn.value,
                            id: btn.id,
                            className: btn.className
                        };
                    } else if (text.includes('cancel') || text.includes('close')) {
                        nav.cancelButton = {
                            text: btn.textContent || btn.value,
                            id: btn.id,
                            className: btn.className
                        };
                    }
                });
                
                return nav;
            });
            
            Object.assign(navigation, navInfo);
            
        } catch (error) {
            this.log('warn', 'Failed to analyze navigation', { error: error.message });
        }
        
        return navigation;
    }

    /**
     * 生成备选选择器
     */
    generateAlternativeSelectors(element) {
        const selectors = [];
        
        // ID选择器
        if (element.id) {
            selectors.push(`#${element.id}`);
        }
        
        // Name选择器
        if (element.name) {
            selectors.push(`[name="${element.name}"]`);
            selectors.push(`${element.tagName}[name="${element.name}"]`);
        }
        
        // Type选择器
        if (element.type && element.type !== 'unknown') {
            selectors.push(`${element.tagName}[type="${element.type}"]`);
        }
        
        // Class选择器
        if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim());
            classes.forEach(cls => {
                selectors.push(`.${cls}`);
                selectors.push(`${element.tagName}.${cls}`);
            });
        }
        
        // Placeholder选择器
        if (element.placeholder) {
            selectors.push(`[placeholder="${element.placeholder}"]`);
        }
        
        return [...new Set(selectors)]; // 去重
    }

    /**
     * 生成字段映射配置
     */
    generateFieldMapping(pageType) {
        const structure = this.pageStructures.get(pageType);
        if (!structure) {
            throw new Error(`No structure found for page type: ${pageType}`);
        }
        
        const mapping = {};
        
        structure.formFields.forEach(field => {
            // 根据字段特征推断用途
            const fieldPurpose = this.inferFieldPurpose(field);
            
            if (fieldPurpose) {
                mapping[fieldPurpose] = {
                    primarySelector: this.selectBestSelector(field.detectedSelectors),
                    alternativeSelectors: field.detectedSelectors,
                    fieldType: field.type,
                    required: field.required,
                    validation: this.getFieldValidation(field, structure.validationRules)
                };
            }
        });
        
        return mapping;
    }

    /**
     * 推断字段用途
     */
    inferFieldPurpose(field) {
        const indicators = [
            field.name?.toLowerCase() || '',
            field.id?.toLowerCase() || '',
            field.placeholder?.toLowerCase() || '',
            field.className?.toLowerCase() || ''
        ].join(' ');
        
        // 邮箱字段
        if (indicators.includes('email') || field.type === 'email') {
            return 'email';
        }
        
        // 密码字段
        if (indicators.includes('password') || field.type === 'password') {
            return 'password';
        }
        
        // 确认密码字段
        if (indicators.includes('confirm') && indicators.includes('password')) {
            return 'confirmPassword';
        }
        
        // 名字字段
        if (indicators.includes('firstname') || indicators.includes('first_name')) {
            return 'firstName';
        }
        
        // 姓氏字段
        if (indicators.includes('lastname') || indicators.includes('last_name')) {
            return 'lastName';
        }
        
        // 电话字段
        if (indicators.includes('phone') || indicators.includes('tel') || field.type === 'tel') {
            return 'phone';
        }
        
        // 生日字段
        if (indicators.includes('birth') || indicators.includes('dob') || field.type === 'date') {
            return 'birthDate';
        }
        
        // SSN字段
        if (indicators.includes('ssn') || indicators.includes('social')) {
            return 'ssn';
        }
        
        return null; // 无法识别的字段
    }

    /**
     * 选择最佳选择器
     */
    selectBestSelector(selectors) {
        // 优先级：ID > Name > Type > Class
        const priorities = ['#', '[name=', '[type=', '.'];
        
        for (const priority of priorities) {
            const selector = selectors.find(s => s.startsWith(priority));
            if (selector) return selector;
        }
        
        return selectors[0] || '';
    }

    /**
     * 获取字段验证规则
     */
    getFieldValidation(field, validationRules) {
        const fieldName = field.name || field.id;
        return validationRules.find(rule => rule.name === fieldName) || {};
    }

    /**
     * 保存页面结构分析结果
     */
    async saveAnalysisResults(outputPath) {
        const results = {
            timestamp: new Date().toISOString(),
            pages: Object.fromEntries(this.pageStructures),
            fieldMappings: Object.fromEntries(this.fieldMappings)
        };
        
        const fs = await import('fs-extra');
        await fs.writeFile(outputPath, JSON.stringify(results, null, 2));
        
        this.log('info', 'Analysis results saved', { outputPath });
        
        return results;
    }

    /**
     * 检测页面变化
     */
    async detectPageChanges(pageType, previousStructure) {
        try {
            const currentStructure = await this.analyzePageStructure(pageType);
            const changes = this.compareStructures(previousStructure, currentStructure);

            if (changes.length > 0) {
                this.log('warn', 'Page structure changes detected', {
                    pageType,
                    changesCount: changes.length,
                    changes
                });
            }

            return changes;

        } catch (error) {
            this.log('error', 'Failed to detect page changes', { error: error.message });
            return [];
        }
    }

    /**
     * 比较页面结构
     */
    compareStructures(oldStructure, newStructure) {
        const changes = [];

        // 比较字段数量
        if (oldStructure.formFields.length !== newStructure.formFields.length) {
            changes.push({
                type: 'field_count_change',
                old: oldStructure.formFields.length,
                new: newStructure.formFields.length
            });
        }

        // 比较具体字段
        const oldFields = new Set(oldStructure.formFields.map(f => f.name || f.id));
        const newFields = new Set(newStructure.formFields.map(f => f.name || f.id));

        // 新增字段
        const addedFields = [...newFields].filter(f => !oldFields.has(f));
        if (addedFields.length > 0) {
            changes.push({
                type: 'fields_added',
                fields: addedFields
            });
        }

        // 删除字段
        const removedFields = [...oldFields].filter(f => !newFields.has(f));
        if (removedFields.length > 0) {
            changes.push({
                type: 'fields_removed',
                fields: removedFields
            });
        }

        return changes;
    }

    /**
     * 分析页面结构（通用方法）
     */
    async analyzePageStructure(pageType) {
        switch (pageType) {
            case 'registration':
                return await this.analyzeRegistrationPage();
            case 'application':
                return await this.analyzeApplicationPage();
            default:
                throw new Error(`Unknown page type: ${pageType}`);
        }
    }

    /**
     * 日志记录辅助方法
     */
    log(level, message, meta = {}) {
        if (this.logger) {
            this.logger.log(level, message, { ...meta, component: 'UNRPageAnalyzer' });
        } else {
            console.log(`[${level.toUpperCase()}] ${message}`, meta);
        }
    }
}
