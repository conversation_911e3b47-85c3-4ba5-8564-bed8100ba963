/**
 * UNR申请表单自动化流程
 * 
 * 功能：
 * 1. 自动完成10个申请步骤
 * 2. 智能表单填写
 * 3. 数据验证和提交
 * 4. 流程状态管理
 * 
 * <AUTHOR> Registration Bot
 */

/**
 * 申请流程控制器
 */
export class ApplicationFlow {
    constructor(browserController, config = {}) {
        this.browser = browserController;
        this.logger = config.logger || null;
        this.config = {
            applyUrl: config.applyUrl || 'https://admissions.unr.edu/apply/',
            loginUrl: config.loginUrl || 'https://admissions.unr.edu/account/login',
            maxRetries: config.maxRetries || 3,
            stepDelay: config.stepDelay || 2000,
            ...config
        };
        
        this.currentStep = 0;
        this.totalSteps = 10;
        this.applicationData = null;
        this.completedSteps = [];
        
        // 定义10个申请步骤
        this.steps = [
            { id: 1, name: 'Application Information', handler: 'handleApplicationInformation' },
            { id: 2, name: 'Nondegree Application Instructions', handler: 'handleNondegreeInstructions' },
            { id: 3, name: 'Application Term', handler: 'handleApplicationTerm' },
            { id: 4, name: 'Personal Background', handler: 'handlePersonalBackground' },
            { id: 5, name: 'Background Continued', handler: 'handleBackgroundContinued' },
            { id: 6, name: 'Emergency Contact', handler: 'handleEmergencyContact' },
            { id: 7, name: 'Academic History', handler: 'handleAcademicHistory' },
            { id: 8, name: 'Program Selection', handler: 'handleProgramSelection' },
            { id: 9, name: 'Signature', handler: 'handleSignature' },
            { id: 10, name: 'Review', handler: 'handleReview' }
        ];
    }

    /**
     * 执行完整申请流程
     */
    async execute(studentData) {
        try {
            console.log('\n🚀 开始UNR申请表单流程');
            console.log('=====================================');
            console.log(`👤 学生: ${studentData.firstName} ${studentData.lastName}`);
            console.log(`📧 邮箱: ${studentData.email}`);
            console.log(`📋 总步骤: ${this.totalSteps}`);

            if (this.logger) {
                this.logger.logFlowStart('Application Flow', {
                    studentName: `${studentData.firstName} ${studentData.lastName}`,
                    email: studentData.email,
                    totalSteps: this.totalSteps
                });
            }

            this.applicationData = studentData;

            // 步骤0: 登录到申请系统
            if (this.logger) {
                this.logger.logApplication('info', 'Starting login to application system');
            }
            await this.loginToApplication(studentData);

            // 执行10个申请步骤
            for (let i = 0; i < this.steps.length; i++) {
                const step = this.steps[i];
                this.currentStep = step.id;

                console.log(`\n📝 步骤${step.id}: ${step.name}`);
                console.log('=====================================');

                if (this.logger) {
                    this.logger.logStepStart(step.name, step.id, {
                        handler: step.handler,
                        studentEmail: studentData.email
                    });
                }

                try {
                    const stepStartTime = Date.now();

                    // 调用对应的处理方法
                    await this[step.handler](studentData);

                    const stepDuration = Date.now() - stepStartTime;
                    this.completedSteps.push(step.id);
                    console.log(`✅ 步骤${step.id}完成: ${step.name}`);

                    if (this.logger) {
                        this.logger.logStepEnd(step.name, step.id, {
                            success: true,
                            duration: stepDuration,
                            completedSteps: this.completedSteps.length
                        });

                        this.logger.logPerformance(`Application Step ${step.id}`, stepDuration, {
                            stepName: step.name,
                            stepId: step.id
                        });
                    }

                    // 步骤间延迟
                    await this.browser.sleep(this.config.stepDelay);

                } catch (error) {
                    console.error(`❌ 步骤${step.id}失败: ${error.message}`);

                    if (this.logger) {
                        this.logger.logError(error, {
                            step: step.name,
                            stepId: step.id,
                            stepHandler: step.handler,
                            studentEmail: studentData.email
                        });
                    }

                    await this.handleStepError(step, error);
                    throw error;
                }
            }

            console.log('\n🎉 UNR申请表单流程完成！');

            const result = {
                success: true,
                completedSteps: this.completedSteps,
                totalSteps: this.totalSteps
            };

            if (this.logger) {
                this.logger.logFlowEnd('Application Flow', {
                    success: true,
                    completedSteps: this.completedSteps.length,
                    totalSteps: this.totalSteps,
                    studentEmail: studentData.email
                });
            }

            return result;

        } catch (error) {
            console.error('❌ 申请流程失败:', error.message);

            if (this.logger) {
                this.logger.logError(error, {
                    flow: 'Application Flow',
                    currentStep: this.currentStep,
                    completedSteps: this.completedSteps,
                    studentEmail: studentData.email
                });
            }

            await this.handleApplicationError(error);
            throw error;
        }
    }

    /**
     * 登录到申请系统（如果需要）
     */
    async loginToApplication(studentData) {
        try {
            console.log('\n🔐 检查登录状态...');

            // 首先尝试直接访问申请页面
            await this.browser.navigateTo(this.config.applyUrl);
            await this.browser.sleep(2000);

            const currentUrl = await this.browser.getCurrentUrl();
            const pageTitle = await this.browser.getPageTitle();

            console.log(`📄 当前页面: ${pageTitle}`);
            console.log(`🔗 当前URL: ${currentUrl}`);

            // 检查是否已经登录（如果URL包含apply或application，说明已登录）
            if (currentUrl.includes('apply') || currentUrl.includes('application')) {
                console.log('✅ 已经登录，直接进入申请系统');
                return;
            }

            // 检查是否被重定向到登录页面
            if (currentUrl.includes('login') || pageTitle.toLowerCase().includes('login')) {
                console.log('🔐 需要登录，开始登录流程...');

                // 填写登录信息
                const emailSelectors = ['input[name="email"]', 'input[type="email"]', '#email'];
                await this.fillFieldWithSelectors(emailSelectors, studentData.email, '邮箱');

                const passwordSelectors = ['input[name="password"]', 'input[type="password"]', '#password'];
                await this.fillFieldWithSelectors(passwordSelectors, studentData.password, '密码');

                // 点击登录按钮
                const loginSelectors = ['button[type="submit"]', 'input[type="submit"]', '#login-btn'];
                await this.clickWithSelectors(loginSelectors, '登录按钮');

                // 等待登录完成
                await this.browser.sleep(3000);

                // 再次检查是否成功登录
                const newUrl = await this.browser.getCurrentUrl();
                if (newUrl.includes('apply') || newUrl.includes('application')) {
                    console.log('✅ 登录成功，已进入申请系统');
                } else {
                    // 如果还没有进入申请页面，尝试再次导航
                    console.log('🔄 尝试导航到申请页面...');
                    await this.browser.navigateTo(this.config.applyUrl);
                    await this.browser.sleep(2000);
                }
            } else {
                // 如果不是登录页面，尝试查找登录链接
                console.log('🔍 查找登录链接...');
                const loginLinkSelectors = [
                    'a[href*="login"]',
                    'a:contains("Login")',
                    'a:contains("Sign In")',
                    '#login-link'
                ];

                let loginLinkFound = false;
                for (const selector of loginLinkSelectors) {
                    try {
                        await this.browser.waitForElement(selector, { timeout: 3000 });
                        await this.browser.clickElement(selector);
                        console.log('✅ 找到并点击登录链接');
                        loginLinkFound = true;
                        break;
                    } catch (error) {
                        continue;
                    }
                }

                if (loginLinkFound) {
                    // 递归调用，重新检查登录状态
                    await this.loginToApplication(studentData);
                } else {
                    console.log('⚠️ 未找到登录入口，尝试直接导航到登录页面');
                    await this.browser.navigateTo(this.config.loginUrl);
                    await this.loginToApplication(studentData);
                }
            }

        } catch (error) {
            console.error('❌ 登录过程失败:', error.message);
            await this.browser.takeScreenshot('login-error');
            throw new Error(`登录失败: ${error.message}`);
        }
    }

    /**
     * 步骤1: Application Information
     */
    async handleApplicationInformation(studentData) {
        try {
            await this.browser.takeScreenshot('step01-application-info-start');
            
            // 选择申请类型
            console.log('📋 选择申请类型...');
            const applicationTypeSelectors = [
                'select[name="applicationType"]',
                'select[name="application_type"]',
                '#application-type'
            ];
            await this.selectWithSelectors(applicationTypeSelectors, 'undergraduate', '申请类型');
            
            // 填写个人信息确认
            console.log('👤 确认个人信息...');
            await this.confirmPersonalInfo(studentData);
            
            // 点击继续按钮
            await this.clickContinueButton();
            
            await this.browser.takeScreenshot('step01-application-info-complete');
            
        } catch (error) {
            await this.browser.takeScreenshot('step01-application-info-error');
            throw error;
        }
    }

    /**
     * 步骤2: Nondegree Application Instructions
     */
    async handleNondegreeInstructions(studentData) {
        try {
            await this.browser.takeScreenshot('step02-instructions-start');
            
            console.log('📖 阅读申请说明...');
            
            // 等待说明页面加载
            await this.browser.waitForLoadState('domcontentloaded');
            
            // 查找并勾选确认复选框
            const confirmSelectors = [
                'input[type="checkbox"][name*="confirm"]',
                'input[type="checkbox"][name*="agree"]',
                'input[type="checkbox"][name*="understand"]'
            ];
            
            for (const selector of confirmSelectors) {
                try {
                    await this.browser.waitForElement(selector, { timeout: 3000 });
                    await this.browser.clickElement(selector);
                    console.log('✅ 已确认阅读说明');
                    break;
                } catch (error) {
                    continue;
                }
            }
            
            // 点击继续按钮
            await this.clickContinueButton();
            
            await this.browser.takeScreenshot('step02-instructions-complete');
            
        } catch (error) {
            await this.browser.takeScreenshot('step02-instructions-error');
            throw error;
        }
    }

    /**
     * 步骤3: Application Term
     */
    async handleApplicationTerm(studentData) {
        try {
            await this.browser.takeScreenshot('step03-term-start');
            
            console.log('📅 选择申请学期...');
            
            // 选择申请学期
            const termSelectors = [
                'select[name="term"]',
                'select[name="application_term"]',
                '#term-select'
            ];
            
            // 默认选择下一个可用学期
            await this.selectWithSelectors(termSelectors, 'fall-2024', '申请学期');
            
            // 选择入学类型
            const entryTypeSelectors = [
                'select[name="entryType"]',
                'select[name="entry_type"]',
                '#entry-type'
            ];
            await this.selectWithSelectors(entryTypeSelectors, 'first-time', '入学类型');
            
            // 点击继续按钮
            await this.clickContinueButton();
            
            await this.browser.takeScreenshot('step03-term-complete');
            
        } catch (error) {
            await this.browser.takeScreenshot('step03-term-error');
            throw error;
        }
    }

    /**
     * 步骤4: Personal Background
     */
    async handlePersonalBackground(studentData) {
        try {
            await this.browser.takeScreenshot('step04-background-start');
            
            console.log('👤 填写个人背景信息...');
            
            // 填写生日
            if (studentData.birthDate) {
                const birthDateSelectors = ['input[name="birthDate"]', '#birth-date'];
                await this.fillFieldWithSelectors(birthDateSelectors, studentData.birthDate, '生日');
            }
            
            // 选择性别
            if (studentData.gender) {
                const genderSelectors = ['select[name="gender"]', '#gender'];
                await this.selectWithSelectors(genderSelectors, studentData.gender.toLowerCase(), '性别');
            }
            
            // 填写SSN
            if (studentData.ssn) {
                const ssnSelectors = ['input[name="ssn"]', 'input[name="socialSecurityNumber"]', '#ssn'];
                await this.fillFieldWithSelectors(ssnSelectors, studentData.ssn, 'SSN');
            }
            
            // 填写地址信息
            await this.fillAddressInfo(studentData);
            
            // 点击继续按钮
            await this.clickContinueButton();
            
            await this.browser.takeScreenshot('step04-background-complete');
            
        } catch (error) {
            await this.browser.takeScreenshot('step04-background-error');
            throw error;
        }
    }

    /**
     * 步骤5: Background Continued
     */
    async handleBackgroundContinued(studentData) {
        try {
            await this.browser.takeScreenshot('step05-background-cont-start');
            
            console.log('📋 填写背景信息续页...');
            
            // 填写电话号码
            if (studentData.phone) {
                const phoneSelectors = ['input[name="phone"]', 'input[name="phoneNumber"]', '#phone'];
                await this.fillFieldWithSelectors(phoneSelectors, studentData.phone, '电话号码');
            }
            
            // 选择公民身份
            const citizenshipSelectors = ['select[name="citizenship"]', '#citizenship'];
            await this.selectWithSelectors(citizenshipSelectors, 'us-citizen', '公民身份');
            
            // 选择种族/民族（可选）
            const ethnicitySelectors = ['select[name="ethnicity"]', '#ethnicity'];
            await this.selectWithSelectors(ethnicitySelectors, 'prefer-not-to-answer', '种族/民族');
            
            // 点击继续按钮
            await this.clickContinueButton();
            
            await this.browser.takeScreenshot('step05-background-cont-complete');
            
        } catch (error) {
            await this.browser.takeScreenshot('step05-background-cont-error');
            throw error;
        }
    }

    /**
     * 步骤6: Emergency Contact
     */
    async handleEmergencyContact(studentData) {
        try {
            await this.browser.takeScreenshot('step06-emergency-start');
            
            console.log('🚨 填写紧急联系人信息...');
            
            const emergencyContact = studentData.emergencyContact || {
                firstName: 'John',
                lastName: 'Smith',
                relationship: 'other',
                email: '<EMAIL>',
                language: 'English'
            };
            
            // 填写紧急联系人姓名
            const firstNameSelectors = ['input[name="emergencyFirstName"]', '#emergency-first-name'];
            await this.fillFieldWithSelectors(firstNameSelectors, emergencyContact.firstName, '紧急联系人名字');
            
            const lastNameSelectors = ['input[name="emergencyLastName"]', '#emergency-last-name'];
            await this.fillFieldWithSelectors(lastNameSelectors, emergencyContact.lastName, '紧急联系人姓氏');
            
            // 选择关系
            const relationshipSelectors = ['select[name="emergencyRelationship"]', '#emergency-relationship'];
            await this.selectWithSelectors(relationshipSelectors, emergencyContact.relationship, '关系');
            
            // 填写邮箱
            const emailSelectors = ['input[name="emergencyEmail"]', '#emergency-email'];
            await this.fillFieldWithSelectors(emailSelectors, emergencyContact.email, '紧急联系人邮箱');
            
            // 选择语言
            const languageSelectors = ['select[name="emergencyLanguage"]', '#emergency-language'];
            await this.selectWithSelectors(languageSelectors, emergencyContact.language, '语言');
            
            // 点击继续按钮
            await this.clickContinueButton();
            
            await this.browser.takeScreenshot('step06-emergency-complete');
            
        } catch (error) {
            await this.browser.takeScreenshot('step06-emergency-error');
            throw error;
        }
    }

    /**
     * 步骤7: Academic History
     */
    async handleAcademicHistory(studentData) {
        try {
            await this.browser.takeScreenshot('step07-academic-start');
            
            console.log('🎓 填写学术历史...');
            
            const highSchool = studentData.highSchool || {
                schoolName: 'Maplewood High School',
                startDate: { month: 'August', year: '2020' },
                endDate: { month: 'July', year: '2024' },
                graduated: true
            };
            
            // 填写高中信息
            const schoolNameSelectors = ['input[name="highSchoolName"]', '#high-school-name'];
            await this.fillFieldWithSelectors(schoolNameSelectors, highSchool.schoolName, '高中名称');
            
            // 选择开始日期
            const startMonthSelectors = ['select[name="startMonth"]', '#start-month'];
            await this.selectWithSelectors(startMonthSelectors, highSchool.startDate.month, '开始月份');
            
            const startYearSelectors = ['select[name="startYear"]', '#start-year'];
            await this.selectWithSelectors(startYearSelectors, highSchool.startDate.year, '开始年份');
            
            // 选择结束日期
            const endMonthSelectors = ['select[name="endMonth"]', '#end-month'];
            await this.selectWithSelectors(endMonthSelectors, highSchool.endDate.month, '结束月份');
            
            const endYearSelectors = ['select[name="endYear"]', '#end-year'];
            await this.selectWithSelectors(endYearSelectors, highSchool.endDate.year, '结束年份');
            
            // 选择是否毕业
            const graduatedSelectors = ['select[name="graduated"]', '#graduated'];
            await this.selectWithSelectors(graduatedSelectors, highSchool.graduated ? 'yes' : 'no', '是否毕业');
            
            // 点击继续按钮
            await this.clickContinueButton();
            
            await this.browser.takeScreenshot('step07-academic-complete');
            
        } catch (error) {
            await this.browser.takeScreenshot('step07-academic-error');
            throw error;
        }
    }

    /**
     * 步骤8: Program Selection
     */
    async handleProgramSelection(studentData) {
        try {
            await this.browser.takeScreenshot('step08-program-start');
            
            console.log('🎯 选择申请项目...');
            
            // 选择学院
            const collegeSelectors = ['select[name="college"]', '#college'];
            await this.selectWithSelectors(collegeSelectors, 'liberal-arts', '学院');
            
            // 选择专业
            const majorSelectors = ['select[name="major"]', '#major'];
            await this.selectWithSelectors(majorSelectors, 'computer-science', '专业');
            
            // 选择学位类型
            const degreeSelectors = ['select[name="degreeType"]', '#degree-type'];
            await this.selectWithSelectors(degreeSelectors, 'bachelor', '学位类型');
            
            // 点击继续按钮
            await this.clickContinueButton();
            
            await this.browser.takeScreenshot('step08-program-complete');
            
        } catch (error) {
            await this.browser.takeScreenshot('step08-program-error');
            throw error;
        }
    }

    /**
     * 步骤9: Signature
     */
    async handleSignature(studentData) {
        try {
            await this.browser.takeScreenshot('step09-signature-start');
            
            console.log('✍️ 电子签名...');
            
            // 填写签名
            const signatureSelectors = ['input[name="signature"]', '#signature'];
            const fullName = `${studentData.firstName} ${studentData.lastName}`;
            await this.fillFieldWithSelectors(signatureSelectors, fullName, '电子签名');
            
            // 填写签名日期
            const dateSelectors = ['input[name="signatureDate"]', '#signature-date'];
            const today = new Date().toISOString().split('T')[0];
            await this.fillFieldWithSelectors(dateSelectors, today, '签名日期');
            
            // 同意条款
            const agreeSelectors = ['input[type="checkbox"][name*="agree"]', '#agree-terms'];
            await this.clickWithSelectors(agreeSelectors, '同意条款');
            
            // 点击继续按钮
            await this.clickContinueButton();
            
            await this.browser.takeScreenshot('step09-signature-complete');
            
        } catch (error) {
            await this.browser.takeScreenshot('step09-signature-error');
            throw error;
        }
    }

    /**
     * 步骤10: Review
     */
    async handleReview(studentData) {
        try {
            await this.browser.takeScreenshot('step10-review-start');
            
            console.log('👀 审核申请信息...');
            
            // 等待审核页面加载
            await this.browser.waitForLoadState('domcontentloaded');
            
            // 检查信息是否正确
            console.log('🔍 检查申请信息...');
            
            // 最终提交
            console.log('📤 提交申请...');
            const submitSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("Submit")',
                '#submit-application'
            ];
            
            await this.clickWithSelectors(submitSelectors, '提交申请');
            
            // 等待提交完成
            await this.browser.sleep(5000);
            
            await this.browser.takeScreenshot('step10-review-complete');
            
            console.log('🎉 申请已成功提交！');
            
        } catch (error) {
            await this.browser.takeScreenshot('step10-review-error');
            throw error;
        }
    }

    /**
     * 工具方法：使用多个选择器填写字段
     */
    async fillFieldWithSelectors(selectors, value, fieldName) {
        for (const selector of selectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.fillInput(selector, value);
                console.log(`✅ ${fieldName}填写成功: ${value}`);
                return;
            } catch (error) {
                continue;
            }
        }
        console.warn(`⚠️ 未找到${fieldName}字段，跳过`);
    }

    /**
     * 工具方法：使用多个选择器选择选项
     */
    async selectWithSelectors(selectors, value, fieldName) {
        for (const selector of selectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.selectOption(selector, value);
                console.log(`✅ ${fieldName}选择成功: ${value}`);
                return;
            } catch (error) {
                continue;
            }
        }
        console.warn(`⚠️ 未找到${fieldName}选择框，跳过`);
    }

    /**
     * 工具方法：使用多个选择器点击元素
     */
    async clickWithSelectors(selectors, elementName) {
        for (const selector of selectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: 3000 });
                await this.browser.clickElement(selector);
                console.log(`✅ ${elementName}点击成功`);
                return;
            } catch (error) {
                continue;
            }
        }
        console.warn(`⚠️ 未找到${elementName}，跳过`);
    }

    /**
     * 点击继续按钮
     */
    async clickContinueButton() {
        const continueSelectors = [
            'button:contains("Continue")',
            'button:contains("Next")',
            'input[value="Continue"]',
            'input[value="Next"]',
            '#continue-btn',
            '#next-btn'
        ];
        
        await this.clickWithSelectors(continueSelectors, '继续按钮');
        await this.browser.sleep(2000);
    }

    /**
     * 确认个人信息
     */
    async confirmPersonalInfo(studentData) {
        // 检查并确认个人信息是否正确
        console.log('✅ 个人信息确认完成');
    }

    /**
     * 填写地址信息
     */
    async fillAddressInfo(studentData) {
        if (studentData.address) {
            const streetSelectors = ['input[name="street"]', '#street-address'];
            await this.fillFieldWithSelectors(streetSelectors, studentData.address.street, '街道地址');
            
            const citySelectors = ['input[name="city"]', '#city'];
            await this.fillFieldWithSelectors(citySelectors, studentData.address.city, '城市');
            
            const stateSelectors = ['select[name="state"]', '#state'];
            await this.selectWithSelectors(stateSelectors, studentData.address.state, '州');
            
            const zipSelectors = ['input[name="zip"]', '#zip-code'];
            await this.fillFieldWithSelectors(zipSelectors, studentData.address.zipCode, '邮编');
        }
    }

    /**
     * 处理步骤错误
     */
    async handleStepError(step, error) {
        console.error(`❌ 步骤${step.id}错误: ${error.message}`);
        await this.browser.takeScreenshot(`error-step-${step.id}`);
    }

    /**
     * 处理申请错误
     */
    async handleApplicationError(error) {
        console.error(`❌ 申请流程错误: ${error.message}`);
        await this.browser.takeScreenshot('application-error');
        
        console.log(`🔍 错误发生在步骤: ${this.currentStep}`);
        console.log(`✅ 已完成步骤: ${this.completedSteps.join(', ')}`);
    }
}
