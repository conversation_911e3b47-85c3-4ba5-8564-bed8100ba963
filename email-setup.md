# QQ邮箱IMAP配置指南

## 前置要求

在使用邮件自动化功能之前，需要先配置QQ邮箱的IMAP服务。

## 配置步骤

### 1. 开启QQ邮箱IMAP服务

1. **登录QQ邮箱**
   - 访问 https://mail.qq.com
   - 使用QQ账号登录

2. **进入设置页面**
   - 点击页面右上角的"设置"
   - 选择"账户"选项卡

3. **开启IMAP服务**
   - 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
   - 开启"IMAP/SMTP服务"
   - 按照提示发送短信验证

4. **获取授权码**
   - 开启服务后，系统会生成一个16位的授权码
   - **重要**: 这个授权码就是程序中使用的密码，不是QQ密码
   - 请妥善保存这个授权码

### 2. 配置程序

在项目的`.env`文件中配置邮箱信息：

```env
EMAIL_SERVER=imap.qq.com
EMAIL_PORT=993
EMAIL_USER=你的QQ邮箱@qq.com
EMAIL_PASSWORD=16位授权码
EMAIL_FOLDER=INBOX
```

### 3. 测试连接

运行测试命令验证配置：

```bash
npm test
```

如果配置正确，应该看到：
```
✅ 邮箱连接测试成功
💡 邮件模块已就绪，可以接收验证邮件
```

## 邮件处理流程

### 自动化流程

1. **注册账户** → 触发验证邮件发送
2. **监听邮箱** → 自动检查新邮件
3. **识别验证邮件** → 匹配来自UNR的邮件
4. **提取PIN码** → 从邮件内容中提取9位PIN码
5. **返回验证信息** → 提供PIN码和激活链接

### 邮件识别规则

系统会自动识别符合以下条件的邮件：
- **发件人**: 包含 `unr.edu` 域名
- **主题**: 包含 `Nevada` 关键词
- **状态**: 未读邮件
- **内容**: 包含9位数字PIN码

### PIN码提取模式

支持多种PIN码格式：
```
PIN: 886758143
temporary PIN: 886758143
enter the following PIN: 886758143
886758143
```

## 故障排除

### 常见问题

1. **连接失败**
   ```
   ❌ IMAP连接失败: Invalid credentials
   ```
   **解决方案**: 检查邮箱地址和授权码是否正确

2. **服务未开启**
   ```
   ❌ IMAP连接失败: Connection refused
   ```
   **解决方案**: 确认已开启QQ邮箱IMAP服务

3. **网络问题**
   ```
   ❌ IMAP连接失败: getaddrinfo ENOTFOUND
   ```
   **解决方案**: 检查网络连接和防火墙设置

4. **找不到验证邮件**
   ```
   ⚠️ 未找到验证邮件
   ```
   **解决方案**: 
   - 检查垃圾邮件文件夹
   - 确认邮箱地址正确
   - 等待邮件发送（可能有延迟）

### 调试模式

如需查看详细的邮件处理日志，可以在代码中启用调试模式：

```javascript
const emailHandler = new EmailHandler({
    user: '<EMAIL>',
    password: 'your-auth-code',
    debug: true  // 启用调试模式
});
```

## 安全注意事项

1. **授权码保护**
   - 授权码等同于密码，请妥善保管
   - 不要在代码中硬编码授权码
   - 使用环境变量存储敏感信息

2. **权限最小化**
   - 建议使用专门的邮箱账户
   - 定期更换授权码

3. **网络安全**
   - 确保在安全的网络环境中运行
   - 避免在公共网络中使用

## 技术参数

- **IMAP服务器**: imap.qq.com
- **端口**: 993
- **加密**: TLS/SSL
- **协议**: IMAP4
- **超时时间**: 60秒
- **重试间隔**: 10秒
- **最大等待时间**: 5分钟

## API使用示例

### 基本用法

```javascript
import { EmailHandler } from './utils/emailHandler.js';

const emailHandler = new EmailHandler({
    user: '<EMAIL>',
    password: 'your-auth-code'
});

// 获取PIN码
const pinCode = await emailHandler.getPinCode();
console.log('PIN码:', pinCode);

// 获取完整验证信息
const verificationInfo = await emailHandler.getVerificationInfo();
console.log('验证信息:', verificationInfo);
```

### 高级用法

```javascript
// 自定义等待时间
const verificationInfo = await emailHandler.getVerificationInfo({
    maxWaitTime: 600000  // 10分钟
});

// 测试连接
const isConnected = await emailHandler.testConnection();
if (isConnected) {
    console.log('邮箱连接正常');
}
```
