/**
 * Excel数据读取模块
 * 
 * 功能：
 * 1. 读取test.xlsx文件中的学生信息
 * 2. 解析多个工作表数据
 * 3. 数据验证和格式化
 * 4. 提供结构化的学生数据对象
 * 
 * <AUTHOR> Registration Bot
 */

import XLSX from 'xlsx';
import fs from 'fs-extra';
import path from 'path';

/**
 * Excel数据读取器类
 */
export class ExcelReader {
    constructor(filePath) {
        this.filePath = filePath;
        this.workbook = null;
        this.studentData = null;
    }

    /**
     * 读取Excel文件
     */
    async readFile() {
        try {
            // 检查文件是否存在
            if (!await fs.pathExists(this.filePath)) {
                throw new Error(`Excel文件不存在: ${this.filePath}`);
            }

            console.log(`📊 正在读取Excel文件: ${this.filePath}`);
            
            // 读取工作簿
            const buffer = await fs.readFile(this.filePath);
            this.workbook = XLSX.read(buffer, { type: 'buffer' });
            
            console.log(`✅ Excel文件读取成功，包含工作表: ${this.workbook.SheetNames.join(', ')}`);
            
            return this.workbook;
        } catch (error) {
            console.error('❌ Excel文件读取失败:', error.message);
            throw error;
        }
    }

    /**
     * 解析学生基本信息（主工作表）
     */
    parseStudentInfo() {
        try {
            // 获取第一个工作表（通常是主数据表）
            const sheetName = this.workbook.SheetNames[0];
            const worksheet = this.workbook.Sheets[sheetName];
            
            console.log(`📋 正在解析工作表: ${sheetName}`);
            
            // 转换为JSON格式
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            
            if (jsonData.length < 2) {
                throw new Error('Excel文件数据不足，至少需要标题行和一行数据');
            }
            
            // 获取标题行和数据行
            const headers = jsonData[0];
            const dataRow = jsonData[1]; // 假设只处理第一行学生数据
            
            console.log('📝 Excel标题行:', headers);
            
            // 创建学生数据对象
            const studentInfo = {};
            headers.forEach((header, index) => {
                if (header && dataRow[index] !== undefined) {
                    studentInfo[header] = dataRow[index];
                }
            });
            
            // 数据标准化和验证
            const normalizedData = this.normalizeStudentData(studentInfo);
            
            console.log('✅ 学生基本信息解析完成');
            return normalizedData;
            
        } catch (error) {
            console.error('❌ 学生信息解析失败:', error.message);
            throw error;
        }
    }

    /**
     * 解析高中信息
     */
    parseHighSchoolInfo() {
        try {
            // 查找高中信息工作表
            const hsSheetName = this.workbook.SheetNames.find(name => 
                name.includes('高中') || name.toLowerCase().includes('high') || name.toLowerCase().includes('school')
            );
            
            if (!hsSheetName) {
                console.log('⚠️ 未找到高中信息工作表，使用默认值');
                return {
                    schoolName: 'Maplewood High School',
                    startDate: { month: 'August', year: '2020' },
                    endDate: { month: 'July', year: '2024' },
                    graduated: true
                };
            }
            
            const worksheet = this.workbook.Sheets[hsSheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            
            console.log(`📋 正在解析高中信息工作表: ${hsSheetName}`);
            
            if (jsonData.length >= 2) {
                const headers = jsonData[0];
                const dataRow = jsonData[1];
                
                const schoolInfo = {};
                headers.forEach((header, index) => {
                    if (header && dataRow[index] !== undefined) {
                        schoolInfo[header] = dataRow[index];
                    }
                });
                
                return this.normalizeSchoolData(schoolInfo);
            }
            
            return {
                schoolName: 'Maplewood High School',
                startDate: { month: 'August', year: '2020' },
                endDate: { month: 'July', year: '2024' },
                graduated: true
            };
            
        } catch (error) {
            console.error('❌ 高中信息解析失败:', error.message);
            // 返回默认值
            return {
                schoolName: 'Maplewood High School',
                startDate: { month: 'August', year: '2020' },
                endDate: { month: 'July', year: '2024' },
                graduated: true
            };
        }
    }

    /**
     * 标准化学生数据
     */
    normalizeStudentData(rawData) {
        const normalized = {
            // 基本信息
            email: this.findValue(rawData, ['邮箱', 'email', 'Email', 'EMAIL']),
            firstName: this.findValue(rawData, ['名', 'first name', 'firstName', 'First Name']),
            lastName: this.findValue(rawData, ['姓', 'last name', 'lastName', 'Last Name']),
            birthDate: this.findValue(rawData, ['生日', 'birth date', 'birthDate', 'Birth Date']),
            
            // 联系信息
            phone: this.findValue(rawData, ['电话', 'phone', 'Phone', 'PHONE']),
            
            // 地址信息
            fullAddress: this.findValue(rawData, ['完整地址', 'full address', 'address', 'Address']),
            
            // 个人信息
            gender: this.findValue(rawData, ['性别', 'gender', 'Gender', 'GENDER']),
            ssn: this.findValue(rawData, ['社会安全号', 'ssn', 'SSN', 'Social Security Number']),
        };
        
        // 解析地址信息
        if (normalized.fullAddress) {
            normalized.address = this.parseAddress(normalized.fullAddress);
        }
        
        // 格式化SSN（去除连字符）
        if (normalized.ssn) {
            normalized.ssn = normalized.ssn.toString().replace(/-/g, '');
        }
        
        // 格式化电话号码
        if (normalized.phone) {
            normalized.phone = normalized.phone.toString();
        }
        
        // 验证必要字段
        this.validateStudentData(normalized);
        
        return normalized;
    }

    /**
     * 标准化学校数据
     */
    normalizeSchoolData(rawData) {
        return {
            schoolName: this.findValue(rawData, ['学校名称', 'school name', 'School Name', 'schoolName']) || 'Maplewood High School',
            startDate: {
                month: 'August',
                year: '2020'
            },
            endDate: {
                month: 'July', 
                year: '2024'
            },
            graduated: true
        };
    }

    /**
     * 查找字段值（支持多种可能的字段名）
     */
    findValue(data, possibleKeys) {
        for (const key of possibleKeys) {
            if (data[key] !== undefined && data[key] !== null && data[key] !== '') {
                return data[key];
            }
        }
        return null;
    }

    /**
     * 解析地址信息
     */
    parseAddress(fullAddress) {
        try {
            // 示例: "Camp Chase Trail, Columbus, OH 43328, United States"
            const parts = fullAddress.split(',').map(part => part.trim());
            
            if (parts.length >= 3) {
                return {
                    street: parts[0],
                    city: parts[1],
                    state: parts[2].split(' ')[0], // 提取州缩写
                    zipCode: parts[2].split(' ')[1] || '', // 提取邮编
                    country: parts[3] || 'United States'
                };
            }
            
            // 如果格式不匹配，返回默认值
            return {
                street: 'Camp Chase Trail',
                city: 'Columbus',
                state: 'OH',
                zipCode: '43328',
                country: 'United States'
            };
        } catch (error) {
            console.warn('⚠️ 地址解析失败，使用默认地址');
            return {
                street: 'Camp Chase Trail',
                city: 'Columbus', 
                state: 'OH',
                zipCode: '43328',
                country: 'United States'
            };
        }
    }

    /**
     * 验证学生数据完整性
     */
    validateStudentData(data) {
        const requiredFields = ['email', 'firstName', 'lastName'];
        const missingFields = requiredFields.filter(field => !data[field]);
        
        if (missingFields.length > 0) {
            throw new Error(`缺少必要字段: ${missingFields.join(', ')}`);
        }
        
        // 验证邮箱格式
        if (data.email && !this.isValidEmail(data.email)) {
            throw new Error(`邮箱格式无效: ${data.email}`);
        }
    }

    /**
     * 验证邮箱格式
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * 读取完整的学生数据
     */
    async readStudentData() {
        try {
            // 读取Excel文件
            await this.readFile();
            
            // 解析学生基本信息
            const studentInfo = this.parseStudentInfo();
            
            // 解析高中信息
            const highSchoolInfo = this.parseHighSchoolInfo();
            
            // 组合完整数据
            this.studentData = {
                ...studentInfo,
                highSchool: highSchoolInfo,
                // 添加一些默认值
                emergencyContact: {
                    firstName: 'John',
                    lastName: 'Smith',
                    relationship: 'other',
                    email: '<EMAIL>',
                    language: 'English'
                }
            };
            
            console.log('✅ 学生数据读取完成');
            console.log('📋 学生信息摘要:', {
                姓名: `${this.studentData.firstName} ${this.studentData.lastName}`,
                邮箱: this.studentData.email,
                地址: this.studentData.address?.city,
                高中: this.studentData.highSchool?.schoolName
            });
            
            return this.studentData;
            
        } catch (error) {
            console.error('❌ 学生数据读取失败:', error.message);
            throw error;
        }
    }

    /**
     * 获取已读取的学生数据
     */
    getStudentData() {
        return this.studentData;
    }
}
