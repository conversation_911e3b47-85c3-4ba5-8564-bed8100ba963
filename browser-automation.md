# Puppeteer浏览器自动化指南

## 功能概述

浏览器控制模块基于Puppeteer，提供完整的网页自动化功能：
- 浏览器启动和管理
- 页面导航和操作
- 元素查找和交互
- 表单填写和提交
- 截图和调试支持

## 核心特性

### 1. 智能元素等待
- 自动等待元素出现
- 支持可见性检查
- 可配置超时时间

### 2. 真实用户模拟
- 模拟真实输入速度
- 自动滚动到元素位置
- 智能点击和填写

### 3. 错误处理和调试
- 自动截图保存
- 详细错误日志
- 调试模式支持

### 4. 跨平台兼容
- Windows/macOS/Linux支持
- 自动Chrome检测
- 无头模式支持

## 使用方法

### 基本用法

```javascript
import { BrowserController } from './automation/browserController.js';

// 创建浏览器控制器
const browser = new BrowserController({
    headless: false,    // 显示浏览器窗口
    width: 1280,        // 窗口宽度
    height: 720,        // 窗口高度
    timeout: 30000      // 默认超时30秒
});

// 启动浏览器
await browser.launch();

// 导航到页面
await browser.navigateTo('https://example.com');

// 填写表单
await browser.fillInput('#email', '<EMAIL>');
await browser.fillInput('#password', 'password123');

// 点击按钮
await browser.clickElement('#submit-btn');

// 等待页面加载
await browser.waitForNavigation();

// 关闭浏览器
await browser.close();
```

### 高级用法

```javascript
// 等待特定元素
await browser.waitForElement('.success-message', {
    timeout: 10000,
    visible: true
});

// 选择下拉框
await browser.selectOption('#country', 'US');

// 截图
await browser.takeScreenshot('form-filled', {
    fullPage: true
});

// 获取页面信息
const title = await browser.getPageTitle();
const url = await browser.getCurrentUrl();
```

## 配置选项

### 浏览器配置

```javascript
const config = {
    // 基本设置
    headless: false,           // 是否无头模式
    width: 1280,              // 视口宽度
    height: 720,              // 视口高度
    timeout: 30000,           // 默认超时时间
    
    // 截图设置
    screenshotPath: './screenshots',  // 截图保存路径
    
    // Chrome启动参数
    args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security'
    ]
};
```

### 环境变量配置

```env
# 浏览器配置
BROWSER_HEADLESS=false
BROWSER_WIDTH=1280
BROWSER_HEIGHT=720
BROWSER_TIMEOUT=30000

# 调试配置
DEBUG_MODE=true
SCREENSHOT_ON_ERROR=true
```

## API参考

### BrowserController类

#### 构造函数
```javascript
new BrowserController(config)
```

#### 生命周期方法
- `launch()` - 启动浏览器
- `close()` - 关闭浏览器
- `isReady()` - 检查浏览器状态

#### 导航方法
- `navigateTo(url, options)` - 导航到URL
- `waitForNavigation(options)` - 等待导航完成
- `getCurrentUrl()` - 获取当前URL
- `getPageTitle()` - 获取页面标题

#### 元素操作方法
- `waitForElement(selector, options)` - 等待元素出现
- `clickElement(selector, options)` - 点击元素
- `fillInput(selector, value, options)` - 填写输入框
- `selectOption(selector, value, options)` - 选择下拉框

#### 工具方法
- `takeScreenshot(name, options)` - 截图
- `sleep(ms)` - 延时等待
- `waitForLoadState(state, timeout)` - 等待页面状态

## 测试验证

### 运行测试

```bash
# 测试所有功能
npm test

# 单独测试浏览器功能
npm run test:browser
```

### 测试项目

1. **浏览器启动关闭** - 验证基本生命周期
2. **页面导航** - 测试URL导航功能
3. **元素交互** - 验证点击和填写功能
4. **UNR网站访问** - 测试目标网站连接
5. **截图功能** - 验证调试支持

## 故障排除

### 常见问题

1. **浏览器启动失败**
   ```
   ❌ Error: Failed to launch the browser process
   ```
   **解决方案**:
   - 确认Chrome浏览器已安装
   - 检查系统权限
   - 尝试无头模式

2. **元素查找超时**
   ```
   ❌ TimeoutError: waiting for selector
   ```
   **解决方案**:
   - 检查选择器是否正确
   - 增加超时时间
   - 等待页面完全加载

3. **网络连接问题**
   ```
   ❌ net::ERR_INTERNET_DISCONNECTED
   ```
   **解决方案**:
   - 检查网络连接
   - 配置代理设置
   - 检查防火墙

### 调试技巧

1. **启用可视模式**
   ```javascript
   const browser = new BrowserController({ headless: false });
   ```

2. **增加延时**
   ```javascript
   await browser.sleep(2000); // 等待2秒
   ```

3. **截图调试**
   ```javascript
   await browser.takeScreenshot('debug-point');
   ```

4. **查看页面源码**
   ```javascript
   const content = await browser.page.content();
   console.log(content);
   ```

## 性能优化

### 启动优化

```javascript
const config = {
    headless: true,           // 无头模式更快
    args: [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-images',    // 禁用图片加载
        '--disable-javascript' // 如果不需要JS
    ]
};
```

### 内存管理

```javascript
// 及时关闭页面
await page.close();

// 清理资源
await browser.close();
```

### 并发控制

```javascript
// 限制并发数量
const maxConcurrent = 3;
const semaphore = new Semaphore(maxConcurrent);
```

## 安全注意事项

1. **数据保护**
   - 避免在截图中暴露敏感信息
   - 及时清理临时文件

2. **网站政策**
   - 遵守目标网站的robots.txt
   - 控制请求频率
   - 尊重服务条款

3. **资源使用**
   - 合理设置超时时间
   - 及时释放浏览器资源
   - 监控内存使用

## 最佳实践

1. **错误处理**
   ```javascript
   try {
       await browser.clickElement('#button');
   } catch (error) {
       await browser.takeScreenshot('error-state');
       throw error;
   }
   ```

2. **等待策略**
   ```javascript
   // 等待网络空闲
   await browser.navigateTo(url, { waitUntil: 'networkidle2' });
   
   // 等待特定元素
   await browser.waitForElement('.loading', { timeout: 5000 });
   ```

3. **选择器策略**
   ```javascript
   // 优先使用稳定的选择器
   const selectors = [
       '#submit-button',           // ID选择器（最稳定）
       'button[type="submit"]',    // 属性选择器
       '.submit-btn',              // 类选择器
       'button:contains("Submit")' // 文本选择器（最后备选）
   ];
   ```

## 集成示例

完整的UNR注册自动化示例请参考：
- `src/automation/registrationFlow.js` - 注册流程
- `src/automation/applicationFlow.js` - 申请流程
- `src/test-browser.js` - 测试示例
