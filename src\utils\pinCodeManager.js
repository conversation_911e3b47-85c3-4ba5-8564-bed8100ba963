/**
 * PIN码管理器
 * 
 * 功能：
 * 1. 从Excel读取邮箱列表
 * 2. 为每个邮箱获取对应的PIN码
 * 3. 管理多邮箱的验证流程
 * 
 * <AUTHOR> Registration Bot
 */

import { EmailHandler } from './emailHandler.js';

/**
 * PIN码管理器类
 */
export class PinCodeManager {
    constructor(config = {}) {
        this.config = config;
        this.emailHandlers = new Map(); // 存储不同邮箱的处理器
        this.pinCodes = new Map(); // 存储邮箱对应的PIN码
        this.defaultPassword = config.defaultPassword || 'defaultpassword123'; // 默认邮箱密码
    }

    /**
     * 为指定邮箱创建邮件处理器
     */
    createEmailHandler(email, password = null) {
        const emailPassword = password || this.defaultPassword;
        
        console.log(`📧 为邮箱 ${email} 创建邮件处理器`);
        
        const handler = new EmailHandler({
            email: email,
            user: email,
            password: emailPassword
        });
        
        this.emailHandlers.set(email, handler);
        return handler;
    }

    /**
     * 获取指定邮箱的PIN码
     */
    async getPinCodeForEmail(email, password = null, options = {}) {
        try {
            console.log(`\n🔍 开始获取邮箱 ${email} 的PIN码`);
            
            // 创建或获取邮件处理器
            let handler = this.emailHandlers.get(email);
            if (!handler) {
                handler = this.createEmailHandler(email, password);
            }
            
            // 获取验证信息
            const verificationInfo = await handler.getVerificationInfo({
                maxWaitTime: options.maxWaitTime || 300000 // 5分钟超时
            });
            
            if (verificationInfo && verificationInfo.pinCode) {
                console.log(`✅ 成功获取邮箱 ${email} 的PIN码: ${verificationInfo.pinCode}`);
                
                // 存储PIN码
                this.pinCodes.set(email, {
                    pinCode: verificationInfo.pinCode,
                    activationLink: verificationInfo.activationLink,
                    timestamp: new Date(),
                    email: email
                });
                
                return verificationInfo.pinCode;
            } else {
                throw new Error('未能获取到PIN码');
            }
            
        } catch (error) {
            console.error(`❌ 获取邮箱 ${email} 的PIN码失败:`, error.message);
            throw error;
        }
    }

    /**
     * 批量获取多个邮箱的PIN码
     */
    async getPinCodesForEmails(emails, password = null, options = {}) {
        console.log(`\n📧 开始批量获取 ${emails.length} 个邮箱的PIN码`);
        
        const results = [];
        const concurrent = options.concurrent || 1; // 默认串行处理，避免并发过多
        
        // 分批处理邮箱
        for (let i = 0; i < emails.length; i += concurrent) {
            const batch = emails.slice(i, i + concurrent);
            
            const batchPromises = batch.map(async (email) => {
                try {
                    const pinCode = await this.getPinCodeForEmail(email, password, options);
                    return { email, pinCode, success: true };
                } catch (error) {
                    console.error(`❌ 邮箱 ${email} 处理失败:`, error.message);
                    return { email, error: error.message, success: false };
                }
            });
            
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
            
            // 批次间延迟，避免过于频繁的请求
            if (i + concurrent < emails.length) {
                console.log(`⏳ 等待 ${options.batchDelay || 5000}ms 后处理下一批...`);
                await this.sleep(options.batchDelay || 5000);
            }
        }
        
        // 输出结果统计
        const successful = results.filter(r => r.success);
        const failed = results.filter(r => !r.success);
        
        console.log(`\n📊 批量处理结果:`);
        console.log(`✅ 成功: ${successful.length}/${emails.length}`);
        console.log(`❌ 失败: ${failed.length}/${emails.length}`);
        
        if (failed.length > 0) {
            console.log(`\n失败的邮箱:`);
            failed.forEach(f => console.log(`  - ${f.email}: ${f.error}`));
        }
        
        return results;
    }

    /**
     * 从学生数据中获取PIN码
     */
    async getPinCodeFromStudentData(studentData, options = {}) {
        const email = studentData.email;
        
        if (!email) {
            throw new Error('学生数据中未找到邮箱地址');
        }
        
        console.log(`\n👤 为学生 ${studentData.firstName} ${studentData.lastName} 获取PIN码`);
        console.log(`📧 邮箱地址: ${email}`);
        
        try {
            const pinCode = await this.getPinCodeForEmail(email, null, options);
            
            // 将PIN码添加到学生数据中
            studentData.pinCode = pinCode;
            studentData.pinCodeTimestamp = new Date();
            
            return pinCode;
        } catch (error) {
            console.error(`❌ 为学生获取PIN码失败:`, error.message);
            throw error;
        }
    }

    /**
     * 获取已存储的PIN码
     */
    getStoredPinCode(email) {
        const stored = this.pinCodes.get(email);
        if (stored) {
            console.log(`📋 找到已存储的PIN码: ${email} -> ${stored.pinCode}`);
            return stored;
        }
        return null;
    }

    /**
     * 获取所有已存储的PIN码
     */
    getAllStoredPinCodes() {
        const results = [];
        for (const [email, data] of this.pinCodes.entries()) {
            results.push({
                email,
                pinCode: data.pinCode,
                timestamp: data.timestamp
            });
        }
        return results;
    }

    /**
     * 清除指定邮箱的PIN码缓存
     */
    clearPinCode(email) {
        this.pinCodes.delete(email);
        console.log(`🗑️ 已清除邮箱 ${email} 的PIN码缓存`);
    }

    /**
     * 清除所有PIN码缓存
     */
    clearAllPinCodes() {
        this.pinCodes.clear();
        console.log(`🗑️ 已清除所有PIN码缓存`);
    }

    /**
     * 测试邮箱连接
     */
    async testEmailConnection(email, password = null) {
        try {
            console.log(`🧪 测试邮箱 ${email} 的连接...`);
            
            const handler = this.createEmailHandler(email, password);
            const result = await handler.testConnection();
            
            if (result) {
                console.log(`✅ 邮箱 ${email} 连接测试成功`);
            } else {
                console.log(`❌ 邮箱 ${email} 连接测试失败`);
            }
            
            return result;
        } catch (error) {
            console.error(`❌ 邮箱 ${email} 连接测试异常:`, error.message);
            return false;
        }
    }

    /**
     * 工具方法：延时
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取管理器状态
     */
    getStatus() {
        return {
            totalEmails: this.emailHandlers.size,
            totalPinCodes: this.pinCodes.size,
            emails: Array.from(this.emailHandlers.keys()),
            pinCodes: this.getAllStoredPinCodes()
        };
    }
}
