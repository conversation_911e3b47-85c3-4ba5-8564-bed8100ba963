# UNR针对性脚本使用指南

## 概述

UNR针对性脚本是专门为University of Nevada, Reno (UNR)网站设计的自动化工具集，提供了高度定制化的页面分析、表单填写和申请流程处理功能。

### 核心组件

1. **UNRPageAnalyzer** - 页面结构分析器
2. **UNRFormFiller** - 智能表单填写器
3. **UNRStepProcessor** - 申请步骤处理器
4. **UNRController** - 集成控制器

## 架构设计

### 组件关系图

```
UNRController (集成控制器)
├── UNRPageAnalyzer (页面分析)
├── UNRFormFiller (表单填写)
└── UNRStepProcessor (步骤处理)
```

### 数据流程

```
Excel数据 → UNRController → 页面分析 → 表单填写 → 步骤处理 → 完成申请
```

## 详细功能说明

### 1. UNRPageAnalyzer - 页面结构分析器

#### 功能特性
- **智能页面识别** - 自动识别UNR网站的不同页面类型
- **表单字段分析** - 深度分析表单结构和字段属性
- **动态选择器生成** - 为每个字段生成多个备选选择器
- **页面变化检测** - 监控网站结构变化并自动适配

#### 使用方法

```javascript
import { UNRPageAnalyzer } from './unr/pageAnalyzer.js';

const analyzer = new UNRPageAnalyzer(browserController, {
    logger: logger,
    timeout: 10000
});

// 分析注册页面
const registrationStructure = await analyzer.analyzeRegistrationPage();

// 分析申请页面
const applicationStructure = await analyzer.analyzeApplicationPage();

// 生成字段映射
const fieldMapping = analyzer.generateFieldMapping('registration');
```

#### 分析结果示例

```javascript
{
    pageType: 'registration',
    url: 'https://admissions.unr.edu/account/register',
    title: 'Create Account',
    formFields: [
        {
            tagName: 'input',
            type: 'email',
            name: 'email',
            id: 'email',
            placeholder: 'Enter your email',
            required: true,
            detectedSelectors: ['#email', '[name="email"]', 'input[type="email"]']
        }
    ],
    buttons: [...],
    validationRules: [...]
}
```

### 2. UNRFormFiller - 智能表单填写器

#### 功能特性
- **多选择器策略** - 每个字段支持多个选择器，提高成功率
- **智能选项匹配** - 自动匹配下拉选项的不同表示方式
- **表单验证处理** - 处理UNR特有的表单验证规则
- **填写状态验证** - 验证表单填写结果

#### 字段映射配置

```javascript
// 注册表单字段映射
registration: {
    email: [
        '#email',
        'input[name="email"]',
        'input[type="email"]',
        'input[placeholder*="email" i]'
    ],
    password: [
        '#password',
        'input[name="password"]',
        'input[type="password"]'
    ]
}

// 申请表单字段映射
application: {
    personalInfo: {
        birthDate: ['#birthDate', 'input[name="birthDate"]', 'input[type="date"]'],
        gender: ['#gender', 'select[name="gender"]'],
        ssn: ['#ssn', 'input[name="ssn"]']
    },
    address: {
        street: ['#street', 'input[name="street"]'],
        city: ['#city', 'input[name="city"]'],
        state: ['#state', 'select[name="state"]']
    }
}
```

#### 使用方法

```javascript
import { UNRFormFiller } from './unr/formFiller.js';

const formFiller = new UNRFormFiller(browserController, {
    logger: logger,
    fillDelay: 100
});

// 填写注册表单
await formFiller.fillRegistrationForm(studentData);

// 填写个人信息
await formFiller.fillPersonalInfoForm(studentData);

// 填写地址信息
await formFiller.fillAddressForm(studentData);
```

### 3. UNRStepProcessor - 申请步骤处理器

#### 10个申请步骤

1. **Application Information** - 申请信息
2. **Nondegree Application Instructions** - 申请说明
3. **Application Term** - 申请学期
4. **Personal Background** - 个人背景
5. **Background Continued** - 背景信息续
6. **Emergency Contact** - 紧急联系人
7. **Academic History** - 学术历史
8. **Program Selection** - 项目选择
9. **Signature** - 电子签名
10. **Review** - 审核提交

#### 步骤配置

```javascript
{
    id: 4,
    name: 'Personal Background',
    handler: 'processPersonalBackground',
    expectedElements: ['#birthDate', '#gender', '#ssn'],
    navigationSelectors: ['button:contains("Continue")', '#continue-btn']
}
```

#### 使用方法

```javascript
import { UNRStepProcessor } from './unr/stepProcessor.js';

const stepProcessor = new UNRStepProcessor(browserController, {
    logger: logger,
    stepTimeout: 30000
});

// 处理所有申请步骤
const result = await stepProcessor.processAllSteps(studentData);

// 获取当前步骤信息
const stepInfo = await stepProcessor.getCurrentStepInfo();
```

### 4. UNRController - 集成控制器

#### 功能特性
- **统一API接口** - 提供简单易用的高级API
- **自动状态管理** - 自动处理登录状态和页面跳转
- **自适应策略** - 根据页面分析结果动态调整策略
- **会话状态跟踪** - 完整的会话状态管理

#### 使用方法

```javascript
import { UNRController } from './unr/unrController.js';

const unrController = new UNRController(browserController, {
    logger: logger,
    enablePageAnalysis: true,
    enableAdaptiveStrategy: true
});

// 执行完整注册流程
const registrationResult = await unrController.executeRegistrationFlow(studentData);

// 执行完整申请流程
const applicationResult = await unrController.executeApplicationFlow(studentData);

// 获取会话状态
const sessionStatus = unrController.getSessionStatus();
```

## 配置选项

### 基础配置

```javascript
const config = {
    // 基础设置
    baseUrl: 'https://admissions.unr.edu',
    timeout: 30000,
    retryCount: 3,
    
    // 页面分析
    enablePageAnalysis: true,
    enableAdaptiveStrategy: true,
    
    // 表单填写
    fillDelay: 100,
    waitTimeout: 5000,
    
    // 步骤处理
    stepTimeout: 30000,
    navigationDelay: 2000,
    
    // 日志记录
    logger: loggerInstance
};
```

### UNR特定配置

```javascript
const unrConfig = {
    urls: {
        home: 'https://admissions.unr.edu',
        register: 'https://admissions.unr.edu/account/register',
        login: 'https://admissions.unr.edu/account/login',
        apply: 'https://admissions.unr.edu/apply/',
        dashboard: 'https://admissions.unr.edu/dashboard'
    },
    
    pageIdentifiers: {
        registration: {
            title: ['Create Account', 'Register', 'Sign Up'],
            elements: ['#email', '#password', '#confirmPassword']
        },
        application: {
            title: ['Application', 'Apply', 'Admission Application'],
            elements: ['.application-form', '.step-indicator']
        }
    }
};
```

## 数据格式要求

### 学生数据结构

```javascript
const studentData = {
    // 必填字段
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Smith',
    password: 'generated_password',
    
    // 个人信息
    birthDate: '1995-05-15',
    gender: 'Male',
    ssn: '***********',
    phone: '(*************',
    
    // 地址信息
    address: {
        street: '123 Main St',
        city: 'Las Vegas',
        state: 'NV',
        zipCode: '89101'
    },
    
    // 学术信息
    highSchool: {
        schoolName: 'Las Vegas High School',
        graduationDate: '2024-06-15'
    },
    
    // 紧急联系人
    emergencyContact: {
        firstName: 'Jane',
        lastName: 'Smith',
        relationship: 'Parent',
        email: '<EMAIL>',
        language: 'English'
    }
};
```

## 使用示例

### 基本使用

```javascript
import { UNRController } from './unr/unrController.js';
import { BrowserController } from './automation/browserController.js';
import { Logger } from './utils/logger.js';

// 初始化组件
const browser = new BrowserController({ headless: false });
const logger = new Logger({ enableConsole: true });

await browser.launch();

const unrController = new UNRController(browser, {
    logger: logger,
    enablePageAnalysis: true
});

// 执行完整流程
try {
    // 注册流程
    const registrationResult = await unrController.executeRegistrationFlow(studentData);
    console.log('注册结果:', registrationResult);
    
    // 申请流程
    const applicationResult = await unrController.executeApplicationFlow(studentData);
    console.log('申请结果:', applicationResult);
    
} catch (error) {
    console.error('流程执行失败:', error.message);
} finally {
    await browser.close();
    await logger.close();
}
```

### 高级使用

```javascript
// 启用所有高级功能
const unrController = new UNRController(browser, {
    logger: logger,
    enablePageAnalysis: true,
    enableAdaptiveStrategy: true,
    fillDelay: 50,
    stepTimeout: 60000
});

// 保存会话数据
const sessionData = await unrController.saveSessionData('./session.json');

// 获取详细状态
const status = unrController.getSessionStatus();
console.log('当前页面:', status.currentPage);
console.log('已完成步骤:', status.completedSteps);
```

## 测试和验证

### 运行测试

```bash
# 测试UNR针对性脚本
npm run test:unr

# 测试所有功能
npm test
```

### 测试覆盖

1. **页面分析器测试** - 验证页面结构分析功能
2. **表单填写器测试** - 验证字段映射和填写逻辑
3. **步骤处理器测试** - 验证10个申请步骤处理
4. **集成控制器测试** - 验证组件集成和API接口
5. **数据兼容性测试** - 验证Excel数据格式兼容性
6. **配置映射测试** - 验证字段映射和选项配置

## 故障排除

### 常见问题

#### 1. 页面识别失败
```
❌ Failed to navigate to registration page
```
**解决方案**:
- 检查UNR网站是否可访问
- 验证URL配置是否正确
- 检查页面标识符配置

#### 2. 表单字段未找到
```
⚠️ 未找到邮箱字段，跳过
```
**解决方案**:
- 启用页面分析功能
- 更新字段选择器映射
- 检查网站结构是否变化

#### 3. 步骤导航失败
```
⚠️ Failed to navigate from step 3 with all selectors
```
**解决方案**:
- 检查导航按钮选择器
- 增加步骤超时时间
- 验证步骤完成状态

### 调试技巧

#### 1. 启用详细日志
```javascript
const logger = new Logger({
    level: 'debug',
    enableConsole: true
});
```

#### 2. 禁用无头模式
```javascript
const browser = new BrowserController({
    headless: false,
    timeout: 60000
});
```

#### 3. 查看页面分析结果
```javascript
const structure = await analyzer.analyzeRegistrationPage();
console.log('页面结构:', JSON.stringify(structure, null, 2));
```

## 最佳实践

### 1. 配置优化

```javascript
// 生产环境配置
const productionConfig = {
    enablePageAnalysis: true,
    enableAdaptiveStrategy: true,
    fillDelay: 100,
    retryCount: 3,
    timeout: 30000
};

// 调试环境配置
const debugConfig = {
    enablePageAnalysis: true,
    enableAdaptiveStrategy: false,
    fillDelay: 500,
    retryCount: 1,
    timeout: 60000
};
```

### 2. 错误处理

```javascript
try {
    const result = await unrController.executeApplicationFlow(studentData);
} catch (error) {
    // 记录详细错误信息
    logger.logError(error, {
        operation: 'application_flow',
        studentEmail: studentData.email,
        currentStep: unrController.stepProcessor.currentStepId
    });
    
    // 保存错误状态
    await unrController.saveSessionData('./error-session.json');
    
    throw error;
}
```

### 3. 性能优化

```javascript
// 批量处理多个学生
const students = await excelReader.readAllStudents();

for (const student of students) {
    try {
        await unrController.executeApplicationFlow(student);
        
        // 添加延迟避免过于频繁的请求
        await new Promise(resolve => setTimeout(resolve, 5000));
        
    } catch (error) {
        console.error(`学生 ${student.email} 处理失败:`, error.message);
        continue;
    }
}
```

## 扩展开发

### 自定义步骤处理器

```javascript
class CustomStepProcessor extends UNRStepProcessor {
    async processCustomStep(studentData) {
        // 自定义步骤处理逻辑
        console.log('执行自定义步骤...');
    }
}
```

### 自定义字段映射

```javascript
const customFormFiller = new UNRFormFiller(browser);

// 添加自定义字段映射
customFormFiller.unrFieldMappings.registration.customField = [
    '#customField',
    'input[name="customField"]'
];
```

## 总结

UNR针对性脚本提供了完整的UNR网站自动化解决方案：

✅ **智能页面分析** - 自动适应网站结构变化
✅ **精确表单填写** - 多选择器策略确保高成功率
✅ **完整步骤处理** - 覆盖10个申请步骤的专门处理
✅ **统一集成控制** - 简单易用的高级API接口
✅ **自适应策略** - 根据页面分析结果动态调整
✅ **完善错误处理** - 详细的错误记录和恢复机制

通过这些针对性的优化，系统能够更好地处理UNR网站的特殊要求，提供更稳定和可靠的自动化体验。
