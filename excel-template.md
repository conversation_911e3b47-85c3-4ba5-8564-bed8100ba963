# Excel数据文件格式说明

## 文件名
`test.xlsx`

## 工作表结构

### 主工作表（Sheet1）- 学生基本信息

| 列名 | 中文名 | 示例数据 | 说明 |
|------|--------|----------|------|
| Email | 邮箱 | <EMAIL> | 学生邮箱地址 |
| First Name | 名 | John | 学生名字 |
| Last Name | 姓 | Smith | 学生姓氏 |
| Birth Date | 生日 | 1995-05-15 | 出生日期 |
| Phone | 电话 | (************* | 联系电话 |
| Full Address | 完整地址 | Camp Chase Trail, Columbus, OH 43328, United States | 完整地址信息 |
| Gender | 性别 | Male | 性别（Male/Female） |
| SSN | 社会安全号 | *********** | 社会安全号码 |

### 高中信息工作表（高中信息）

| 列名 | 中文名 | 示例数据 | 说明 |
|------|--------|----------|------|
| School Name | 学校名称 | Maplewood High School | 高中学校名称 |

## 示例数据

### 主工作表数据示例：
```
Email                    | First Name | Last Name | Birth Date | Phone          | Full Address                                      | Gender | SSN
<EMAIL>      | David      | Johnson   | 1995-05-15 | (************* | Camp Chase Trail, Columbus, OH 43328, United States | Male   | ***********
```

### 高中信息工作表数据示例：
```
School Name
Maplewood High School
```

## 注意事项

1. **文件位置**: Excel文件必须放在项目根目录，文件名为`test.xlsx`
2. **工作表名称**: 
   - 主工作表可以是任意名称（通常是Sheet1）
   - 高中信息工作表名称应包含"高中"、"high"或"school"关键词
3. **数据格式**:
   - 第一行必须是标题行
   - 第二行开始是数据行
   - 目前系统只读取第一行学生数据
4. **必填字段**: Email、First Name、Last Name是必填字段
5. **地址格式**: 完整地址应按照"街道, 城市, 州 邮编, 国家"的格式
6. **SSN格式**: 可以包含连字符，系统会自动去除

## 创建Excel文件步骤

1. 打开Excel或WPS表格
2. 创建第一个工作表，输入学生基本信息
3. 创建第二个工作表，命名为"高中信息"，输入学校信息
4. 保存为`test.xlsx`格式
5. 将文件放置在项目根目录

## 测试数据验证

运行以下命令测试Excel文件是否正确：
```bash
npm test
```

系统会自动验证：
- 文件是否存在
- 数据格式是否正确
- 必填字段是否完整
- 邮箱格式是否有效
