/**
 * UNR特定表单填写器
 * 
 * 功能：
 * 1. 智能识别UNR表单字段
 * 2. 自适应表单填写策略
 * 3. 处理UNR特有的表单验证
 * 4. 支持多步骤表单流程
 * 
 * <AUTHOR> Registration Bot
 */

/**
 * UNR表单填写器类
 */
export class UNRFormFiller {
    constructor(browserController, config = {}) {
        this.browser = browserController;
        this.logger = config.logger || null;
        this.config = {
            fillDelay: config.fillDelay || 100,
            retryCount: config.retryCount || 3,
            waitTimeout: config.waitTimeout || 5000,
            ...config
        };
        
        // UNR特定的字段映射
        this.unrFieldMappings = {
            // 注册页面字段
            registration: {
                email: [
                    '#email',
                    'input[name="email"]',
                    'input[type="email"]',
                    'input[placeholder*="email" i]'
                ],
                password: [
                    '#password',
                    'input[name="password"]',
                    'input[type="password"]',
                    'input[placeholder*="password" i]'
                ],
                confirmPassword: [
                    '#confirmPassword',
                    '#confirm_password',
                    'input[name="confirmPassword"]',
                    'input[name="confirm_password"]',
                    'input[placeholder*="confirm" i]'
                ],
                firstName: [
                    '#firstName',
                    '#first_name',
                    'input[name="firstName"]',
                    'input[name="first_name"]',
                    'input[placeholder*="first name" i]'
                ],
                lastName: [
                    '#lastName',
                    '#last_name',
                    'input[name="lastName"]',
                    'input[name="last_name"]',
                    'input[placeholder*="last name" i]'
                ]
            },
            
            // 申请页面字段
            application: {
                personalInfo: {
                    birthDate: [
                        '#birthDate',
                        '#birth_date',
                        'input[name="birthDate"]',
                        'input[name="birth_date"]',
                        'input[type="date"]'
                    ],
                    gender: [
                        '#gender',
                        'select[name="gender"]',
                        'input[name="gender"]'
                    ],
                    ssn: [
                        '#ssn',
                        '#socialSecurityNumber',
                        'input[name="ssn"]',
                        'input[name="socialSecurityNumber"]',
                        'input[placeholder*="ssn" i]'
                    ],
                    phone: [
                        '#phone',
                        '#phoneNumber',
                        'input[name="phone"]',
                        'input[name="phoneNumber"]',
                        'input[type="tel"]'
                    ]
                },
                address: {
                    street: [
                        '#street',
                        '#streetAddress',
                        'input[name="street"]',
                        'input[name="streetAddress"]',
                        'input[placeholder*="street" i]'
                    ],
                    city: [
                        '#city',
                        'input[name="city"]',
                        'input[placeholder*="city" i]'
                    ],
                    state: [
                        '#state',
                        'select[name="state"]',
                        'input[name="state"]'
                    ],
                    zipCode: [
                        '#zipCode',
                        '#zip_code',
                        '#zip',
                        'input[name="zipCode"]',
                        'input[name="zip_code"]',
                        'input[name="zip"]'
                    ]
                },
                academic: {
                    highSchoolName: [
                        '#highSchoolName',
                        '#high_school_name',
                        'input[name="highSchoolName"]',
                        'input[name="high_school_name"]'
                    ],
                    graduationDate: [
                        '#graduationDate',
                        '#graduation_date',
                        'input[name="graduationDate"]',
                        'input[name="graduation_date"]'
                    ]
                }
            }
        };
        
        // UNR特定的选项映射
        this.unrOptionMappings = {
            gender: {
                'male': ['Male', 'M', 'male'],
                'female': ['Female', 'F', 'female'],
                'other': ['Other', 'Prefer not to answer', 'Non-binary']
            },
            state: {
                'NV': ['Nevada', 'NV', 'Nev'],
                'CA': ['California', 'CA', 'Calif'],
                'AZ': ['Arizona', 'AZ', 'Ariz']
            },
            citizenship: {
                'us_citizen': ['US Citizen', 'United States Citizen', 'Citizen'],
                'permanent_resident': ['Permanent Resident', 'Green Card Holder'],
                'international': ['International Student', 'F-1 Visa', 'Other Visa']
            }
        };
    }

    /**
     * 填写注册表单
     */
    async fillRegistrationForm(studentData) {
        try {
            this.log('info', 'Starting registration form fill', {
                email: studentData.email
            });
            
            const fields = this.unrFieldMappings.registration;
            
            // 填写邮箱
            await this.fillField(fields.email, studentData.email, 'Email');
            
            // 填写密码
            await this.fillField(fields.password, studentData.password, 'Password');
            
            // 填写确认密码
            await this.fillField(fields.confirmPassword, studentData.password, 'Confirm Password');
            
            // 填写名字（如果存在）
            if (studentData.firstName) {
                await this.fillField(fields.firstName, studentData.firstName, 'First Name');
            }
            
            // 填写姓氏（如果存在）
            if (studentData.lastName) {
                await this.fillField(fields.lastName, studentData.lastName, 'Last Name');
            }
            
            this.log('info', 'Registration form fill completed');
            
        } catch (error) {
            this.log('error', 'Registration form fill failed', { error: error.message });
            throw error;
        }
    }

    /**
     * 填写申请表单个人信息部分
     */
    async fillPersonalInfoForm(studentData) {
        try {
            this.log('info', 'Starting personal info form fill');
            
            const fields = this.unrFieldMappings.application.personalInfo;
            
            // 填写生日
            if (studentData.birthDate) {
                await this.fillField(fields.birthDate, studentData.birthDate, 'Birth Date');
            }
            
            // 选择性别
            if (studentData.gender) {
                await this.selectOption(fields.gender, studentData.gender, 'Gender', 'gender');
            }
            
            // 填写SSN
            if (studentData.ssn) {
                await this.fillField(fields.ssn, studentData.ssn, 'SSN');
            }
            
            // 填写电话
            if (studentData.phone) {
                await this.fillField(fields.phone, studentData.phone, 'Phone');
            }
            
            this.log('info', 'Personal info form fill completed');
            
        } catch (error) {
            this.log('error', 'Personal info form fill failed', { error: error.message });
            throw error;
        }
    }

    /**
     * 填写地址信息表单
     */
    async fillAddressForm(studentData) {
        try {
            this.log('info', 'Starting address form fill');
            
            if (!studentData.address) {
                this.log('warn', 'No address data provided');
                return;
            }
            
            const fields = this.unrFieldMappings.application.address;
            const address = studentData.address;
            
            // 填写街道地址
            if (address.street) {
                await this.fillField(fields.street, address.street, 'Street Address');
            }
            
            // 填写城市
            if (address.city) {
                await this.fillField(fields.city, address.city, 'City');
            }
            
            // 选择州
            if (address.state) {
                await this.selectOption(fields.state, address.state, 'State', 'state');
            }
            
            // 填写邮编
            if (address.zipCode) {
                await this.fillField(fields.zipCode, address.zipCode, 'Zip Code');
            }
            
            this.log('info', 'Address form fill completed');
            
        } catch (error) {
            this.log('error', 'Address form fill failed', { error: error.message });
            throw error;
        }
    }

    /**
     * 填写学术历史表单
     */
    async fillAcademicHistoryForm(studentData) {
        try {
            this.log('info', 'Starting academic history form fill');
            
            if (!studentData.highSchool) {
                this.log('warn', 'No high school data provided');
                return;
            }
            
            const fields = this.unrFieldMappings.application.academic;
            const highSchool = studentData.highSchool;
            
            // 填写高中名称
            if (highSchool.schoolName) {
                await this.fillField(fields.highSchoolName, highSchool.schoolName, 'High School Name');
            }
            
            // 填写毕业日期
            if (highSchool.graduationDate) {
                await this.fillField(fields.graduationDate, highSchool.graduationDate, 'Graduation Date');
            }
            
            this.log('info', 'Academic history form fill completed');
            
        } catch (error) {
            this.log('error', 'Academic history form fill failed', { error: error.message });
            throw error;
        }
    }

    /**
     * 通用字段填写方法
     */
    async fillField(selectors, value, fieldName) {
        if (!value) {
            this.log('warn', `No value provided for ${fieldName}`);
            return false;
        }
        
        for (const selector of selectors) {
            try {
                // 等待元素出现
                await this.browser.waitForElement(selector, { timeout: this.config.waitTimeout });
                
                // 清空现有内容
                await this.browser.page.evaluate((sel) => {
                    const element = document.querySelector(sel);
                    if (element) {
                        element.value = '';
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                    }
                }, selector);
                
                // 填写新值
                await this.browser.fillInput(selector, value);
                
                // 触发change事件
                await this.browser.page.evaluate((sel) => {
                    const element = document.querySelector(sel);
                    if (element) {
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                        element.dispatchEvent(new Event('blur', { bubbles: true }));
                    }
                }, selector);
                
                this.log('info', `Successfully filled ${fieldName}`, {
                    selector,
                    value: fieldName.toLowerCase().includes('password') ? '***' : value
                });
                
                // 添加填写延迟
                await this.browser.sleep(this.config.fillDelay);
                
                return true;
                
            } catch (error) {
                this.log('debug', `Failed to fill ${fieldName} with selector ${selector}`, {
                    error: error.message
                });
                continue;
            }
        }
        
        this.log('warn', `Failed to fill ${fieldName} with all selectors`);
        return false;
    }

    /**
     * 选择下拉选项
     */
    async selectOption(selectors, value, fieldName, mappingKey) {
        if (!value) {
            this.log('warn', `No value provided for ${fieldName}`);
            return false;
        }
        
        // 获取选项映射
        const optionMapping = this.unrOptionMappings[mappingKey] || {};
        const possibleValues = optionMapping[value.toLowerCase()] || [value];
        
        for (const selector of selectors) {
            try {
                await this.browser.waitForElement(selector, { timeout: this.config.waitTimeout });
                
                // 尝试不同的可能值
                for (const optionValue of possibleValues) {
                    try {
                        await this.browser.selectOption(selector, optionValue);
                        
                        this.log('info', `Successfully selected ${fieldName}`, {
                            selector,
                            value: optionValue
                        });
                        
                        await this.browser.sleep(this.config.fillDelay);
                        return true;
                        
                    } catch (error) {
                        continue;
                    }
                }
                
            } catch (error) {
                this.log('debug', `Failed to select ${fieldName} with selector ${selector}`, {
                    error: error.message
                });
                continue;
            }
        }
        
        this.log('warn', `Failed to select ${fieldName} with all selectors and values`);
        return false;
    }

    /**
     * 验证表单填写结果
     */
    async validateFormFill(expectedFields) {
        const results = {};
        
        for (const [fieldName, selectors] of Object.entries(expectedFields)) {
            let filled = false;
            
            for (const selector of selectors) {
                try {
                    const value = await this.browser.page.$eval(selector, el => el.value || el.textContent);
                    if (value && value.trim()) {
                        filled = true;
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }
            
            results[fieldName] = filled;
        }
        
        return results;
    }

    /**
     * 日志记录辅助方法
     */
    log(level, message, meta = {}) {
        if (this.logger) {
            this.logger.log(level, message, { ...meta, component: 'UNRFormFiller' });
        } else {
            console.log(`[${level.toUpperCase()}] ${message}`, meta);
        }
    }
}
