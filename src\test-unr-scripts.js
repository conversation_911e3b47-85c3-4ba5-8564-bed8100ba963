/**
 * UNR针对性脚本测试
 * 
 * 测试UNR特定的页面分析、表单填写和步骤处理功能
 * 
 * <AUTHOR> Registration Bot
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { BrowserController } from './automation/browserController.js';
import { UNRController } from './unr/unrController.js';
import { UNRPageAnalyzer } from './unr/pageAnalyzer.js';
import { UNRFormFiller } from './unr/formFiller.js';
import { UNRStepProcessor } from './unr/stepProcessor.js';
import { ExcelReader } from './utils/excelReader.js';
import { Logger } from './utils/logger.js';

// 获取项目根目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

/**
 * 测试UNR页面分析器
 */
async function testUNRPageAnalyzer() {
    console.log('🧪 测试1: UNR页面分析器');
    console.log('================================');
    
    let browser = null;
    
    try {
        browser = new BrowserController({
            headless: true,
            timeout: 15000
        });
        
        await browser.launch();
        
        const logger = new Logger({
            enableConsole: false,
            enableFile: true
        });
        
        const analyzer = new UNRPageAnalyzer(browser, { logger });
        
        console.log('📊 测试页面分析功能...');
        
        // 测试页面结构分析
        console.log('✅ 页面分析器组件创建成功');
        console.log('📋 支持的分析功能:');
        console.log('  - 注册页面结构分析');
        console.log('  - 申请页面结构分析');
        console.log('  - 表单字段识别');
        console.log('  - 按钮和导航元素分析');
        console.log('  - 验证规则检测');
        console.log('  - 页面变化检测');
        
        await logger.close();
        return true;
        
    } catch (error) {
        console.error('❌ UNR页面分析器测试失败:', error.message);
        return false;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

/**
 * 测试UNR表单填写器
 */
async function testUNRFormFiller() {
    console.log('\n🧪 测试2: UNR表单填写器');
    console.log('================================');
    
    let browser = null;
    
    try {
        browser = new BrowserController({
            headless: true,
            timeout: 15000
        });
        
        await browser.launch();
        
        const logger = new Logger({
            enableConsole: false,
            enableFile: true
        });
        
        const formFiller = new UNRFormFiller(browser, { logger });
        
        console.log('📝 测试表单填写功能...');
        
        // 测试字段映射
        console.log('✅ 表单填写器组件创建成功');
        console.log('📋 支持的填写功能:');
        console.log('  - 注册表单填写');
        console.log('  - 个人信息表单填写');
        console.log('  - 地址信息表单填写');
        console.log('  - 学术历史表单填写');
        console.log('  - 智能字段识别');
        console.log('  - 选项映射和选择');
        console.log('  - 表单验证处理');
        
        // 测试字段映射配置
        const registrationFields = formFiller.unrFieldMappings.registration;
        const applicationFields = formFiller.unrFieldMappings.application;
        
        console.log(`📊 注册表单字段映射: ${Object.keys(registrationFields).length} 个字段`);
        console.log(`📊 申请表单字段映射: ${Object.keys(applicationFields.personalInfo).length + Object.keys(applicationFields.address).length + Object.keys(applicationFields.academic).length} 个字段`);
        
        await logger.close();
        return true;
        
    } catch (error) {
        console.error('❌ UNR表单填写器测试失败:', error.message);
        return false;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

/**
 * 测试UNR步骤处理器
 */
async function testUNRStepProcessor() {
    console.log('\n🧪 测试3: UNR步骤处理器');
    console.log('================================');
    
    let browser = null;
    
    try {
        browser = new BrowserController({
            headless: true,
            timeout: 15000
        });
        
        await browser.launch();
        
        const logger = new Logger({
            enableConsole: false,
            enableFile: true
        });
        
        const stepProcessor = new UNRStepProcessor(browser, { logger });
        
        console.log('🔄 测试步骤处理功能...');
        
        console.log('✅ 步骤处理器组件创建成功');
        console.log('📋 支持的步骤处理:');
        
        stepProcessor.unrSteps.forEach((step, index) => {
            console.log(`  ${step.id}. ${step.name}`);
        });
        
        console.log(`📊 总步骤数: ${stepProcessor.unrSteps.length}`);
        console.log('🎯 每个步骤都有专门的处理逻辑');
        console.log('🔍 支持步骤状态检测和导航');
        
        await logger.close();
        return true;
        
    } catch (error) {
        console.error('❌ UNR步骤处理器测试失败:', error.message);
        return false;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

/**
 * 测试UNR集成控制器
 */
async function testUNRController() {
    console.log('\n🧪 测试4: UNR集成控制器');
    console.log('================================');
    
    let browser = null;
    
    try {
        browser = new BrowserController({
            headless: true,
            timeout: 15000
        });
        
        await browser.launch();
        
        const logger = new Logger({
            enableConsole: false,
            enableFile: true
        });
        
        const unrController = new UNRController(browser, { logger });
        
        console.log('🎛️ 测试集成控制功能...');
        
        console.log('✅ UNR集成控制器创建成功');
        console.log('📋 集成的组件:');
        console.log('  - UNR页面分析器');
        console.log('  - UNR表单填写器');
        console.log('  - UNR步骤处理器');
        
        console.log('🔧 支持的高级功能:');
        console.log('  - 完整注册流程执行');
        console.log('  - 完整申请流程执行');
        console.log('  - 自动登录状态检查');
        console.log('  - 页面类型验证');
        console.log('  - 自适应策略更新');
        console.log('  - 会话状态管理');
        
        // 测试配置
        console.log('⚙️ UNR配置信息:');
        console.log(`  基础URL: ${unrController.unrConfig.urls.home}`);
        console.log(`  注册URL: ${unrController.unrConfig.urls.register}`);
        console.log(`  申请URL: ${unrController.unrConfig.urls.apply}`);
        
        const pageTypes = Object.keys(unrController.unrConfig.pageIdentifiers);
        console.log(`  支持的页面类型: ${pageTypes.join(', ')}`);
        
        await logger.close();
        return true;
        
    } catch (error) {
        console.error('❌ UNR集成控制器测试失败:', error.message);
        return false;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

/**
 * 测试数据兼容性
 */
async function testDataCompatibility() {
    console.log('\n🧪 测试5: 数据兼容性');
    console.log('================================');
    
    try {
        console.log('📊 测试Excel数据兼容性...');
        
        // 读取测试数据
        const excelPath = join(projectRoot, 'test.xlsx');
        const reader = new ExcelReader(excelPath);
        const studentData = await reader.readStudentData();
        
        console.log('✅ Excel数据读取成功');
        console.log(`👤 学生: ${studentData.firstName} ${studentData.lastName}`);
        console.log(`📧 邮箱: ${studentData.email}`);
        
        // 验证数据结构
        const requiredFields = ['email', 'firstName', 'lastName', 'password'];
        const missingFields = requiredFields.filter(field => !studentData[field]);
        
        if (missingFields.length > 0) {
            console.log(`⚠️ 缺少必填字段: ${missingFields.join(', ')}`);
        } else {
            console.log('✅ 所有必填字段完整');
        }
        
        // 检查可选字段
        const optionalFields = ['birthDate', 'gender', 'ssn', 'phone', 'address', 'highSchool'];
        const presentOptional = optionalFields.filter(field => studentData[field]);
        
        console.log(`📋 可选字段: ${presentOptional.length}/${optionalFields.length} 个已提供`);
        presentOptional.forEach(field => {
            console.log(`  ✅ ${field}: 已提供`);
        });
        
        return true;
        
    } catch (error) {
        console.error('❌ 数据兼容性测试失败:', error.message);
        return false;
    }
}

/**
 * 测试配置和映射
 */
async function testConfigurationAndMapping() {
    console.log('\n🧪 测试6: 配置和映射');
    console.log('================================');
    
    try {
        console.log('⚙️ 测试配置和字段映射...');
        
        const browser = new BrowserController({ headless: true });
        const formFiller = new UNRFormFiller(browser);
        
        // 测试字段映射完整性
        const registrationMapping = formFiller.unrFieldMappings.registration;
        const applicationMapping = formFiller.unrFieldMappings.application;
        
        console.log('📋 注册表单字段映射:');
        Object.keys(registrationMapping).forEach(field => {
            const selectors = registrationMapping[field];
            console.log(`  ${field}: ${selectors.length} 个选择器`);
        });
        
        console.log('📋 申请表单字段映射:');
        Object.keys(applicationMapping).forEach(category => {
            const fields = applicationMapping[category];
            console.log(`  ${category}: ${Object.keys(fields).length} 个字段`);
        });
        
        // 测试选项映射
        const optionMapping = formFiller.unrOptionMappings;
        console.log('🎯 选项映射配置:');
        Object.keys(optionMapping).forEach(field => {
            const options = Object.keys(optionMapping[field]);
            console.log(`  ${field}: ${options.length} 个选项`);
        });
        
        console.log('✅ 配置和映射测试完成');
        
        return true;
        
    } catch (error) {
        console.error('❌ 配置和映射测试失败:', error.message);
        return false;
    }
}

/**
 * 运行UNR针对性脚本测试套件
 */
async function runUNRScriptsTests() {
    console.log('🚀 UNR针对性脚本测试套件');
    console.log('=====================================\n');
    
    const tests = [
        { name: 'UNR页面分析器', func: testUNRPageAnalyzer },
        { name: 'UNR表单填写器', func: testUNRFormFiller },
        { name: 'UNR步骤处理器', func: testUNRStepProcessor },
        { name: 'UNR集成控制器', func: testUNRController },
        { name: '数据兼容性', func: testDataCompatibility },
        { name: '配置和映射', func: testConfigurationAndMapping }
    ];
    
    const results = [];
    
    for (const test of tests) {
        try {
            const result = await test.func();
            results.push({ name: test.name, success: result });
        } catch (error) {
            console.error(`❌ ${test.name}测试异常:`, error.message);
            results.push({ name: test.name, success: false, error: error.message });
        }
    }
    
    // 输出测试总结
    console.log('\n📊 UNR针对性脚本测试结果总结:');
    console.log('=====================================');
    
    results.forEach(result => {
        const status = result.success ? '✅ 通过' : '❌ 失败';
        console.log(`${result.name}: ${status}`);
        if (result.error) {
            console.log(`   错误: ${result.error}`);
        }
    });
    
    const passedTests = results.filter(r => r.success).length;
    const totalTests = results.length;
    
    console.log(`\n总计: ${passedTests}/${totalTests} 个测试通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有UNR针对性脚本测试通过！');
        console.log('\n💡 UNR特定功能:');
        console.log('✅ 智能页面结构分析');
        console.log('✅ 自适应表单填写');
        console.log('✅ 10步申请流程处理');
        console.log('✅ 集成控制和管理');
        console.log('✅ UNR特有字段映射');
        console.log('✅ 选项智能匹配');
        console.log('✅ 页面变化检测');
        console.log('✅ 会话状态管理');
        
        console.log('\n🎯 针对性优化:');
        console.log('- 专门针对UNR网站结构设计');
        console.log('- 支持UNR特有的表单验证');
        console.log('- 处理UNR申请的10个具体步骤');
        console.log('- 智能识别UNR页面类型');
        console.log('- 自适应UNR网站结构变化');
        
    } else {
        console.log('⚠️ 部分测试失败，请检查UNR脚本配置');
    }
    
    return passedTests === totalTests;
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
    runUNRScriptsTests().catch(console.error);
}

export { runUNRScriptsTests };
