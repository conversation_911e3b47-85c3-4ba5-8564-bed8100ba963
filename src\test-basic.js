/**
 * 基础功能测试脚本
 * 
 * 测试不依赖浏览器自动化的核心功能
 * 
 * <AUTHOR> Registration Bot
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

// 获取项目根目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = dirname(__dirname);

/**
 * 测试项目结构
 */
async function testProjectStructure() {
    console.log('🧪 测试1: 项目结构检查');
    console.log('================================');
    
    try {
        const requiredDirs = [
            'src',
            'src/automation',
            'src/utils',
            'src/unr',
            'src/tools'
        ];
        
        const requiredFiles = [
            'package.json',
            'README.md',
            'src/main.js',
            'src/automation/browserController.js',
            'src/automation/registrationFlow.js',
            'src/automation/applicationFlow.js',
            'src/utils/excelReader.js',
            'src/utils/emailProcessor.js',
            'src/utils/logger.js',
            'src/utils/logAnalyzer.js',
            'src/unr/pageAnalyzer.js',
            'src/unr/formFiller.js',
            'src/unr/stepProcessor.js',
            'src/unr/unrController.js'
        ];
        
        console.log('📁 检查目录结构...');
        for (const dir of requiredDirs) {
            const dirPath = join(projectRoot, dir);
            try {
                await fs.promises.access(dirPath);
                console.log(`  ✅ ${dir}`);
            } catch {
                console.log(`  ❌ ${dir}`);
            }
        }
        
        console.log('\n📄 检查核心文件...');
        for (const file of requiredFiles) {
            const filePath = join(projectRoot, file);
            try {
                await fs.promises.access(filePath);
                console.log(`  ✅ ${file}`);
            } catch {
                console.log(`  ❌ ${file}`);
            }
        }
        
        console.log('\n✅ 项目结构检查完成');
        return true;
        
    } catch (error) {
        console.error('❌ 项目结构检查失败:', error.message);
        return false;
    }
}

/**
 * 测试配置文件
 */
async function testConfigFiles() {
    console.log('\n🧪 测试2: 配置文件检查');
    console.log('================================');
    
    try {
        // 检查package.json
        const packagePath = join(projectRoot, 'package.json');
        const packageContent = await fs.promises.readFile(packagePath, 'utf8');
        const packageData = JSON.parse(packageContent);
        
        console.log('📦 package.json 信息:');
        console.log(`  名称: ${packageData.name}`);
        console.log(`  版本: ${packageData.version}`);
        console.log(`  描述: ${packageData.description}`);
        
        // 检查脚本
        const scripts = packageData.scripts || {};
        const expectedScripts = [
            'start',
            'test',
            'test:unr',
            'test:logging',
            'logs:status',
            'logs:analyze'
        ];
        
        console.log('\n📜 npm脚本:');
        for (const script of expectedScripts) {
            const exists = scripts[script];
            console.log(`  ${exists ? '✅' : '❌'} ${script}: ${exists || '未定义'}`);
        }
        
        // 检查依赖
        const dependencies = packageData.dependencies || {};
        const expectedDeps = [
            'playwright',
            'xlsx',
            'fs-extra',
            'dotenv',
            'winston'
        ];
        
        console.log('\n📚 依赖包:');
        for (const dep of expectedDeps) {
            const version = dependencies[dep];
            console.log(`  ${version ? '✅' : '❌'} ${dep}: ${version || '未安装'}`);
        }
        
        console.log('\n✅ 配置文件检查完成');
        return true;
        
    } catch (error) {
        console.error('❌ 配置文件检查失败:', error.message);
        return false;
    }
}

/**
 * 测试模块导入
 */
async function testModuleImports() {
    console.log('\n🧪 测试3: 模块导入检查');
    console.log('================================');
    
    const modules = [
        { name: 'ExcelReader', path: './utils/excelReader.js' },
        { name: 'EmailProcessor', path: './utils/emailProcessor.js' },
        { name: 'Logger', path: './utils/logger.js' },
        { name: 'LogAnalyzer', path: './utils/logAnalyzer.js' },
        { name: 'UNRPageAnalyzer', path: './unr/pageAnalyzer.js' },
        { name: 'UNRFormFiller', path: './unr/formFiller.js' },
        { name: 'UNRStepProcessor', path: './unr/stepProcessor.js' },
        { name: 'UNRController', path: './unr/unrController.js' }
    ];
    
    let successCount = 0;
    
    for (const module of modules) {
        try {
            const moduleExports = await import(module.path);
            const hasClass = moduleExports[module.name];
            
            if (hasClass) {
                console.log(`  ✅ ${module.name}: 导入成功`);
                successCount++;
            } else {
                console.log(`  ❌ ${module.name}: 类未导出`);
            }
            
        } catch (error) {
            console.log(`  ❌ ${module.name}: 导入失败 - ${error.message}`);
        }
    }
    
    console.log(`\n📊 模块导入结果: ${successCount}/${modules.length} 成功`);
    console.log('✅ 模块导入检查完成');
    
    return successCount === modules.length;
}

/**
 * 测试类实例化
 */
async function testClassInstantiation() {
    console.log('\n🧪 测试4: 类实例化检查');
    console.log('================================');
    
    try {
        // 测试Logger类
        const { Logger } = await import('./utils/logger.js');
        const logger = new Logger({
            enableConsole: false,
            enableFile: false
        });
        console.log('  ✅ Logger: 实例化成功');
        
        // 测试LogAnalyzer类
        const { LogAnalyzer } = await import('./utils/logAnalyzer.js');
        const analyzer = new LogAnalyzer({
            logDir: './test-logs',
            outputDir: './test-reports'
        });
        console.log('  ✅ LogAnalyzer: 实例化成功');
        
        // 测试EmailProcessor类
        const { EmailProcessor } = await import('./utils/emailProcessor.js');
        const emailProcessor = new EmailProcessor({
            email: '<EMAIL>',
            password: 'test-password'
        });
        console.log('  ✅ EmailProcessor: 实例化成功');
        
        console.log('\n✅ 类实例化检查完成');
        return true;
        
    } catch (error) {
        console.error('❌ 类实例化检查失败:', error.message);
        return false;
    }
}

/**
 * 测试UNR组件配置
 */
async function testUNRComponents() {
    console.log('\n🧪 测试5: UNR组件配置检查');
    console.log('================================');
    
    try {
        // 测试UNRFormFiller配置
        const { UNRFormFiller } = await import('./unr/formFiller.js');
        const mockBrowser = { page: null };
        const formFiller = new UNRFormFiller(mockBrowser);
        
        console.log('📝 UNRFormFiller 配置:');
        const regFields = Object.keys(formFiller.unrFieldMappings.registration);
        console.log(`  注册表单字段: ${regFields.length} 个`);
        console.log(`  字段: ${regFields.join(', ')}`);
        
        const appFields = formFiller.unrFieldMappings.application;
        const totalAppFields = Object.keys(appFields.personalInfo).length + 
                              Object.keys(appFields.address).length + 
                              Object.keys(appFields.academic).length;
        console.log(`  申请表单字段: ${totalAppFields} 个`);
        
        // 测试UNRStepProcessor配置
        const { UNRStepProcessor } = await import('./unr/stepProcessor.js');
        const stepProcessor = new UNRStepProcessor(mockBrowser);
        
        console.log('\n🔄 UNRStepProcessor 配置:');
        console.log(`  申请步骤数量: ${stepProcessor.unrSteps.length} 个`);
        stepProcessor.unrSteps.forEach(step => {
            console.log(`    ${step.id}. ${step.name}`);
        });
        
        console.log('\n✅ UNR组件配置检查完成');
        return true;
        
    } catch (error) {
        console.error('❌ UNR组件配置检查失败:', error.message);
        return false;
    }
}

/**
 * 测试文档文件
 */
async function testDocumentation() {
    console.log('\n🧪 测试6: 文档文件检查');
    console.log('================================');
    
    try {
        const docFiles = [
            'README.md',
            'logging-guide.md',
            'unr-scripts-guide.md'
        ];
        
        for (const docFile of docFiles) {
            const filePath = join(projectRoot, docFile);

            try {
                const content = await fs.promises.readFile(filePath, 'utf8');
                const lines = content.split('\n').length;
                const size = Math.round(content.length / 1024);
                console.log(`  ✅ ${docFile}: ${lines} 行, ${size} KB`);
            } catch {
                console.log(`  ❌ ${docFile}: 文件不存在`);
            }
        }
        
        console.log('\n✅ 文档文件检查完成');
        return true;
        
    } catch (error) {
        console.error('❌ 文档文件检查失败:', error.message);
        return false;
    }
}

/**
 * 运行基础功能测试套件
 */
async function runBasicTests() {
    console.log('🚀 UNR自动化系统基础功能测试');
    console.log('=====================================\n');
    
    const tests = [
        { name: '项目结构检查', func: testProjectStructure },
        { name: '配置文件检查', func: testConfigFiles },
        { name: '模块导入检查', func: testModuleImports },
        { name: '类实例化检查', func: testClassInstantiation },
        { name: 'UNR组件配置检查', func: testUNRComponents },
        { name: '文档文件检查', func: testDocumentation }
    ];
    
    const results = [];
    
    for (const test of tests) {
        try {
            const result = await test.func();
            results.push({ name: test.name, success: result });
        } catch (error) {
            console.error(`❌ ${test.name}异常:`, error.message);
            results.push({ name: test.name, success: false, error: error.message });
        }
    }
    
    // 输出测试总结
    console.log('\n📊 基础功能测试结果总结:');
    console.log('=====================================');
    
    results.forEach(result => {
        const status = result.success ? '✅ 通过' : '❌ 失败';
        console.log(`${result.name}: ${status}`);
        if (result.error) {
            console.log(`   错误: ${result.error}`);
        }
    });
    
    const passedTests = results.filter(r => r.success).length;
    const totalTests = results.length;
    
    console.log(`\n总计: ${passedTests}/${totalTests} 个测试通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有基础功能测试通过！');
        console.log('\n💡 系统功能概览:');
        console.log('✅ 完整的项目结构');
        console.log('✅ 正确的配置文件');
        console.log('✅ 模块导入正常');
        console.log('✅ 类实例化成功');
        console.log('✅ UNR组件配置完整');
        console.log('✅ 文档文件齐全');
        
        console.log('\n🎯 核心功能:');
        console.log('- 🤖 浏览器自动化控制');
        console.log('- 📊 Excel数据读取处理');
        console.log('- 📧 邮件处理和PIN码提取');
        console.log('- 📝 智能表单填写');
        console.log('- 🔄 多步骤流程处理');
        console.log('- 📋 日志记录和分析');
        console.log('- 🎯 UNR针对性优化');
        
        console.log('\n🚀 可用命令:');
        console.log('npm start          # 启动主程序');
        console.log('npm run test:unr   # UNR脚本测试');
        console.log('npm run logs:status # 查看日志状态');
        console.log('npm run demo:logging # 日志系统演示');
        
    } else {
        console.log('⚠️ 部分测试失败，请检查系统配置');
        
        if (passedTests >= totalTests * 0.8) {
            console.log('💡 大部分功能正常，可以尝试运行基本功能');
        }
    }
    
    return passedTests === totalTests;
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
    runBasicTests().catch(console.error);
}

export { runBasicTests };
