/**
 * 日志系统演示脚本
 * 
 * 演示日志记录系统的各种功能
 * 
 * <AUTHOR> Registration Bot
 */

import { Logger } from './utils/logger.js';
import { LogAnalyzer } from './utils/logAnalyzer.js';
import { LogManager } from './tools/logManager.js';

/**
 * 演示基本日志功能
 */
async function demoBasicLogging() {
    console.log('🎯 演示1: 基本日志功能');
    console.log('=====================================');
    
    const logger = new Logger({
        level: 'debug',
        enableConsole: true,
        enableFile: true
    });
    
    // 等待初始化完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('📝 记录不同级别的日志...');
    
    // 基本日志记录
    logger.info('系统启动成功', { 
        version: '1.0.0',
        environment: 'demo'
    });
    
    logger.warn('检测到潜在问题', {
        issue: 'memory_usage_high',
        threshold: '80%',
        current: '85%'
    });
    
    logger.error('操作失败', {
        operation: 'database_connection',
        error: 'Connection timeout',
        retryCount: 3
    });
    
    logger.debug('调试信息', {
        function: 'demoBasicLogging',
        parameters: { level: 'debug' },
        timestamp: new Date().toISOString()
    });
    
    console.log('✅ 基本日志记录完成');
    
    await logger.close();
}

/**
 * 演示分类日志功能
 */
async function demoCategorizedLogging() {
    console.log('\n🎯 演示2: 分类日志功能');
    console.log('=====================================');
    
    const logger = new Logger({
        level: 'info',
        enableConsole: true,
        enableFile: true
    });
    
    // 等待初始化完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('📂 记录不同分类的日志...');
    
    // 注册流程日志
    logger.logRegistration('info', '用户开始注册', {
        email: '<EMAIL>',
        source: 'web_form'
    });
    
    logger.logRegistration('info', '邮箱验证完成', {
        email: '<EMAIL>',
        verificationTime: 30000
    });
    
    // 申请流程日志
    logger.logApplication('info', '申请流程开始', {
        applicationType: 'undergraduate',
        term: 'fall-2024'
    });
    
    logger.logApplication('info', '申请步骤完成', {
        step: 3,
        stepName: 'Personal Information',
        progress: '30%'
    });
    
    // 邮件处理日志
    logger.logEmail('info', '验证邮件发送', {
        recipient: '<EMAIL>',
        subject: 'Email Verification',
        provider: 'gmail'
    });
    
    logger.logEmail('info', 'PIN码提取成功', {
        pinCode: '123456',
        extractionTime: 2000
    });
    
    // 浏览器操作日志
    logger.logBrowser('info', '浏览器启动', {
        browser: 'chromium',
        headless: false,
        viewport: { width: 1280, height: 720 }
    });
    
    logger.logBrowser('info', '页面导航', {
        url: 'https://admissions.unr.edu',
        loadTime: 3000,
        status: 'success'
    });
    
    console.log('✅ 分类日志记录完成');
    
    await logger.close();
}

/**
 * 演示流程跟踪功能
 */
async function demoFlowTracking() {
    console.log('\n🎯 演示3: 流程跟踪功能');
    console.log('=====================================');
    
    const logger = new Logger({
        level: 'info',
        enableConsole: true,
        enableFile: true
    });
    
    // 等待初始化完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('🔄 模拟完整的注册流程...');
    
    // 开始流程
    logger.logFlowStart('Demo Registration Flow', {
        studentEmail: '<EMAIL>',
        startTime: new Date().toISOString()
    });
    
    // 模拟5个步骤
    const steps = [
        { name: '导航到注册页面', duration: 2000 },
        { name: '填写注册表单', duration: 5000 },
        { name: '提交注册信息', duration: 1500 },
        { name: '等待验证邮件', duration: 10000 },
        { name: '完成邮箱验证', duration: 3000 }
    ];
    
    for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        const stepNumber = i + 1;
        
        // 步骤开始
        logger.logStepStart(step.name, stepNumber, {
            estimatedDuration: step.duration
        });
        
        console.log(`  📝 执行步骤${stepNumber}: ${step.name}`);
        
        // 模拟步骤执行
        await new Promise(resolve => setTimeout(resolve, 200));
        
        // 步骤完成
        logger.logStepEnd(step.name, stepNumber, {
            success: true,
            actualDuration: step.duration,
            progress: `${Math.round((stepNumber / steps.length) * 100)}%`
        });
        
        // 记录性能
        logger.logPerformance(step.name, step.duration, {
            stepNumber,
            category: 'registration'
        });
    }
    
    // 结束流程
    logger.logFlowEnd('Demo Registration Flow', {
        success: true,
        totalSteps: steps.length,
        totalDuration: steps.reduce((sum, step) => sum + step.duration, 0),
        completionRate: '100%'
    });
    
    console.log('✅ 流程跟踪演示完成');
    
    await logger.close();
}

/**
 * 演示错误处理功能
 */
async function demoErrorHandling() {
    console.log('\n🎯 演示4: 错误处理功能');
    console.log('=====================================');
    
    const logger = new Logger({
        level: 'info',
        enableConsole: true,
        enableFile: true
    });
    
    // 等待初始化完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('❌ 模拟各种错误情况...');
    
    // 模拟网络错误
    const networkError = new Error('Network connection failed');
    networkError.name = 'NetworkError';
    logger.logError(networkError, {
        operation: 'page_navigation',
        url: 'https://example.com',
        retryCount: 2,
        context: 'browser_automation'
    });
    
    // 模拟验证错误
    const validationError = new Error('Invalid email format');
    validationError.name = 'ValidationError';
    logger.logError(validationError, {
        operation: 'form_validation',
        field: 'email',
        value: 'invalid-email',
        context: 'user_input'
    });
    
    // 模拟超时错误
    const timeoutError = new Error('Operation timed out after 30 seconds');
    timeoutError.name = 'TimeoutError';
    logger.logError(timeoutError, {
        operation: 'email_verification',
        timeout: 30000,
        context: 'email_processing'
    });
    
    // 模拟系统错误
    const systemError = new Error('Insufficient memory');
    systemError.name = 'SystemError';
    logger.logError(systemError, {
        operation: 'browser_launch',
        memoryUsage: '95%',
        availableMemory: '512MB',
        context: 'system_resources'
    });
    
    console.log('✅ 错误处理演示完成');
    
    await logger.close();
}

/**
 * 演示性能监控功能
 */
async function demoPerformanceMonitoring() {
    console.log('\n🎯 演示5: 性能监控功能');
    console.log('=====================================');
    
    const logger = new Logger({
        level: 'info',
        enableConsole: true,
        enableFile: true
    });
    
    // 等待初始化完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('⚡ 记录性能指标...');
    
    // 模拟各种操作的性能数据
    const operations = [
        { name: 'Excel文件读取', duration: 1200, category: 'file_io' },
        { name: '浏览器启动', duration: 3500, category: 'browser' },
        { name: '页面加载', duration: 2800, category: 'network' },
        { name: '表单填写', duration: 800, category: 'automation' },
        { name: '邮件检查', duration: 5000, category: 'email' },
        { name: 'PIN码提取', duration: 1500, category: 'text_processing' }
    ];
    
    for (const op of operations) {
        logger.logPerformance(op.name, op.duration, {
            category: op.category,
            threshold: 3000,
            status: op.duration > 3000 ? 'slow' : 'normal'
        });
        
        console.log(`  ⏱️ ${op.name}: ${op.duration}ms`);
    }
    
    // 记录用户操作
    logger.logUserAction('click', '#register-button', {
        coordinates: { x: 150, y: 300 },
        timestamp: new Date().toISOString()
    });
    
    logger.logUserAction('type', '#email-input', {
        value: '<EMAIL>',
        inputLength: 16,
        typingSpeed: 50
    });
    
    // 记录网络请求
    logger.logNetworkRequest('POST', '/api/register', 200, 1800, {
        requestSize: 2048,
        responseSize: 1024,
        contentType: 'application/json'
    });
    
    logger.logNetworkRequest('GET', '/api/verify-email', 200, 1200, {
        requestSize: 512,
        responseSize: 256,
        cached: false
    });
    
    console.log('✅ 性能监控演示完成');
    
    await logger.close();
}

/**
 * 演示日志分析功能
 */
async function demoLogAnalysis() {
    console.log('\n🎯 演示6: 日志分析功能');
    console.log('=====================================');
    
    console.log('📊 生成日志分析报告...');
    
    const analyzer = new LogAnalyzer();
    
    // 生成快速报告
    console.log('\n📋 快速分析报告:');
    await analyzer.generateQuickReport();
    
    // 生成完整分析（如果有足够的日志数据）
    try {
        console.log('\n📈 生成完整分析报告...');
        const analysis = await analyzer.analyzeAllLogs();
        
        if (analysis && analysis.summary.totalLogs > 0) {
            console.log(`✅ 分析完成，共处理 ${analysis.summary.totalLogs} 条日志`);
            console.log(`📊 发现 ${analysis.summary.sessions} 个会话`);
            console.log(`❌ 发现 ${analysis.summary.errors} 个错误`);
            console.log(`⚠️ 发现 ${analysis.summary.warnings} 个警告`);
        }
    } catch (error) {
        console.log('⚠️ 完整分析需要更多日志数据');
    }
    
    console.log('✅ 日志分析演示完成');
}

/**
 * 演示日志管理工具
 */
async function demoLogManagement() {
    console.log('\n🎯 演示7: 日志管理工具');
    console.log('=====================================');
    
    console.log('🛠️ 演示日志管理命令...');
    
    const logManager = new LogManager();
    
    // 显示日志状态
    console.log('\n📊 日志状态:');
    await logManager.showStatus();
    
    // 列出日志文件
    console.log('\n📋 日志文件列表:');
    await logManager.listLogFiles();
    
    console.log('✅ 日志管理工具演示完成');
}

/**
 * 运行完整的日志系统演示
 */
async function runLoggingDemo() {
    console.log('🚀 UNR自动化日志系统演示');
    console.log('=====================================');
    console.log('💡 这个演示将展示日志系统的各种功能');
    console.log('📁 日志文件将保存在 logs/ 目录中');
    console.log('📊 分析报告将保存在 reports/ 目录中\n');
    
    try {
        // 运行所有演示
        await demoBasicLogging();
        await demoCategorizedLogging();
        await demoFlowTracking();
        await demoErrorHandling();
        await demoPerformanceMonitoring();
        await demoLogAnalysis();
        await demoLogManagement();
        
        console.log('\n🎉 日志系统演示完成！');
        console.log('=====================================');
        console.log('💡 接下来你可以：');
        console.log('1. 查看 logs/ 目录中的日志文件');
        console.log('2. 运行 npm run logs:analyze 生成分析报告');
        console.log('3. 运行 npm run logs:tail 实时查看日志');
        console.log('4. 运行 npm run logs:export 导出日志数据');
        
    } catch (error) {
        console.error('❌ 演示过程中发生错误:', error.message);
    }
}

// 运行演示
if (import.meta.url === `file://${process.argv[1]}`) {
    runLoggingDemo().catch(console.error);
}

export { runLoggingDemo };
